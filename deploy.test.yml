version: '3.7'
services:
  hyperf:
    image: ${DOCKER_IMAGE_NAME:-fxerp}:${DOCKER_IMAGE_TAG:-latest}
    environment:
      - "APP_PROJECT=fxerp"
      - "APP_ENV=test"
    ports:
      - "${DOCKER_HYPERF_HOST_PORT:-9501}:9501"
      - "${DOCKER_HYPERF_SWAGGER_PORT:-9503}:9503"
    deploy:
      replicas: 1
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      update_config:
        parallelism: 1
        delay: 5s
        order: start-first
