-- auto-generated definition
CREATE TABLE shops
(
    id                       BIGINT UNSIGNED AUTO_INCREMENT COMMENT '授权记录 ID'
        PRIMARY KEY,
    user_id                  INT               NOT NULL COMMENT '用户id, user主键',
    type                     TINYINT DEFAULT 0 NOT NULL COMMENT '授权类型,如taoabo',
    role_type                TINYINT DEFAULT 1 NOT NULL COMMENT '店铺角色类型 0未知 1商家 2厂家',
    identifier               VARCHAR(128)      NOT NULL COMMENT '唯一身份',
    access_token             VARCHAR(500)      NULL COMMENT '授权token',
    refresh_token            VARCHAR(500)      NULL COMMENT '刷新token',
    expire_at                TIMESTAMP         NULL COMMENT 'access token过期时间',
    auth_at                  TIMESTAMP         NULL COMMENT '授权开始时间',
    auth_status              TINYINT DEFAULT 0 NOT NULL COMMENT '授权状态',
    login_count              INT     DEFAULT 0 NOT NULL COMMENT '登录次数',
    shop_name                VARCHAR(100)      NULL COMMENT '店铺名',
    shop_identifier          VARCHAR(100)      NULL COMMENT '多店铺唯一身份',
    shop_logo                VARCHAR(300)      NULL COMMENT '店铺LOGO',
    name                     VARCHAR(100)      NULL COMMENT '用户名',
    auth_user_id             VARCHAR(64)       NULL COMMENT '授权用户id',
    last_sync_page           VARCHAR(255)      NULL COMMENT '同步的页码',
    sync_switch              TINYINT DEFAULT 1 NOT NULL COMMENT '同步开关',
    last_sync_at             TIMESTAMP         NULL COMMENT '最后同步订单时间',
    last_operated_at         TIMESTAMP         NULL COMMENT '最后手动同步操作时间',
    last_factory_sync_at     TIMESTAMP         NULL COMMENT '最后厂商同步时间',
    last_factory_operated_at TIMESTAMP         NULL COMMENT '最后厂商手动同步时间',
    last_goods_sync_at       TIMESTAMP         NULL COMMENT '最后商品同步时间',
    last_refund_sync_at      TIMESTAMP         NULL COMMENT '最后同步退款订单时间',
    original_user_id         INT     DEFAULT 0 NOT NULL COMMENT '原来的用户ID',
    inviter                  INT     DEFAULT 0 NOT NULL COMMENT '邀请绑定用户id',
    service_id               VARCHAR(100)      NULL COMMENT '微信小店使用',
    specification_id         VARCHAR(100)      NULL COMMENT '微信小店使用',
    shop_code                VARCHAR(50)       NULL COMMENT '店铺唯一标识',
    created_at               TIMESTAMP         NULL,
    updated_at               TIMESTAMP         NULL,
    deleted_at               TIMESTAMP         NULL,
    `created_by`             BIGINT            NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`             BIGINT            NOT NULL DEFAULT 0 COMMENT '更新者'
);

CREATE INDEX idx_authuserid
    ON shops (auth_user_id);

CREATE INDEX idx_serviceid
    ON shops (service_id);

CREATE INDEX shops_access_token_index
    ON shops (access_token);

CREATE INDEX shops_auth_at_index
    ON shops (auth_at);

CREATE INDEX shops_created_at_index
    ON shops (created_at);

CREATE INDEX shops_identifier_index
    ON shops (identifier);

CREATE INDEX shops_inviter_index
    ON shops (inviter);

CREATE INDEX shops_shop_code_index
    ON shops (shop_code);

CREATE INDEX shops_type_index
    ON shops (type);

CREATE INDEX shops_user_id_index
    ON shops (user_id);



CREATE TABLE `delivery_template`
(
    `id`            BIGINT AUTO_INCREMENT,
    `user_id`       BIGINT       NOT NULL DEFAULT 0 COMMENT '团队id',
    `size_type`     VARCHAR(100) NOT NULL COMMENT '尺寸 例如A4（210*297）',
    `template_name` VARCHAR(100) NOT NULL COMMENT '模板名称',
    `width`         DOUBLE       NOT NULL COMMENT '宽',
    `height`        DOUBLE       NOT NULL COMMENT '高',
    `content`       JSON         NOT NULL COMMENT '模板内容',
    `created_by`    VARCHAR(20)  NOT NULL COMMENT '创建人', -- This was already present, keeping the original definition.
    `updated_by`    VARCHAR(20)  NOT NULL COMMENT '修改人', -- This was already present, keeping the original definition.
    `created_at`    TIMESTAMP    NOT NULL,
    `updated_at`    TIMESTAMP    NOT NULL,
    PRIMARY KEY (`id`)
);


CREATE TABLE `print_templates`
(
    `id`                 BIGINT AUTO_INCREMENT,
    `user_id`            INTEGER      NOT NULL COMMENT '用户id',
    `style`              TINYINT      NOT NULL DEFAULT 1 COMMENT '模板样式 1=拼多多标准模板，3=拼多多一联单模板',
    `name`               VARCHAR(128) NOT NULL COMMENT '模板名称',
    `width`              DECIMAL      NOT NULL COMMENT '宽',
    `height`             DECIMAL      NOT NULL COMMENT '高',
    `waybill_type`       VARCHAR(100) NOT NULL COMMENT '面单类型',
    `show_logo`          TINYINT      NOT NULL DEFAULT 0 COMMENT '快递公司logo, 0-不显示 1 显示',
    `horizontal`         DOUBLE       NOT NULL DEFAULT 0 COMMENT '水平偏移量',
    `horizontal_type`    INTEGER      NOT NULL DEFAULT 0 COMMENT '水平偏移类型（1：左 2：右）',
    `vertical`           DOUBLE       NOT NULL DEFAULT 0 COMMENT '垂直偏移量',
    `vertical_type`      INTEGER      NOT NULL DEFAULT 0 COMMENT '垂直偏移类型（1：上 2：下）',
    `merge_template_url` TEXT         NOT NULL COMMENT '合并模板地址',
    `wp_code`            VARCHAR(64)  NOT NULL COMMENT '快递公司ID',
    `wp_name`            VARCHAR(64)  NOT NULL COMMENT '快递公司名称',
    `custom_config`      TEXT         NOT NULL COMMENT '商家自定义模板信息',
    `print_contents`     TEXT         NOT NULL COMMENT '模板内容',
    `created_at`         TIMESTAMP    NOT NULL,
    `updated_at`         TIMESTAMP    NOT NULL,
    `deleted_at`         TIMESTAMP    NULL,
    `created_by`         BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`         BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
);

create table waybills
(
    id            bigint unsigned auto_increment comment 'ID'
        primary key,
    user_id       int               not null comment '用户id, user主键',
    shop_id       int               not null comment '店铺id',
    auth_source   tinyint default 0 not null comment '授权来源',
    access_token  varchar(500)      not null comment '电子面单授权token',
    refresh_token varchar(500)      not null comment '电子面单刷新token',
    owner_id      varchar(255)      not null comment 'owner_id',
    owner_name    varchar(255)      not null comment 'owner_name',
    expires_in    int               not null comment 'token有效期',
    expires_at    timestamp         null comment 'token过期时间',
    created_at    timestamp         null,
    updated_at    timestamp         null,
    deleted_at    timestamp         null,
    `created_by`    BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`    BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者'
);

create index idx_shopid_authsource
    on waybills (shop_id, auth_source);

create index waybills_user_id_shop_id_auth_source_index
    on waybills (user_id, shop_id, auth_source);



CREATE TABLE `waybill_share_actions`
(
    `id`            INTEGER AUTO_INCREMENT,
    `user_id`       INTEGER      NOT NULL COMMENT '用户id',
    `shop_id`       INTEGER      NOT NULL COMMENT 'shop_id',
    `company_id`    INTEGER      NOT NULL COMMENT '快递公司id',
    `shop_name`     VARCHAR(255) NOT NULL COMMENT '店铺名',
    `name`          VARCHAR(100) NOT NULL COMMENT '用户名',
    `identifier`    VARCHAR(128) NOT NULL COMMENT '唯一身份',
    `branch_name`   VARCHAR(255) NOT NULL COMMENT '网点名称',
    `wp_code`       VARCHAR(64)  NOT NULL COMMENT '快递公司ID',
    `wp_name`       VARCHAR(64)  NOT NULL COMMENT '快递公司名称',
    `balance_limit` INTEGER      NOT NULL DEFAULT 0 COMMENT '单号数量',
    `province`      VARCHAR(255) NOT NULL COMMENT '省',
    `city`          VARCHAR(255) NOT NULL COMMENT '市',
    `district`      VARCHAR(255) NOT NULL COMMENT '区',
    `street`        VARCHAR(50)  NOT NULL COMMENT '街道',
    `detail`        VARCHAR(255) NOT NULL COMMENT '详细地址',
    `action`        TINYINT      NOT NULL DEFAULT 4 COMMENT '操作状态，0是恢复，1是冻结，2是删除，3是追加，4是新建，5是减少',
    `created_at`    TIMESTAMP    NOT NULL,
    `updated_at`    TIMESTAMP    NOT NULL,
    `auth_source`   TINYINT      NOT NULL COMMENT '面单账号来源',
    `dest_shop_id`  BIGINT       NOT NULL COMMENT '目标店铺id,就是接受面单的店铺id',
    `created_by`    BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`    BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
);


CREATE INDEX `waybill_share_actions_user_id_shop_id_index`
    ON `waybill_share_actions` (`user_id`, `shop_id`);

CREATE TABLE `user_relations`
(
    `id`              BIGINT AUTO_INCREMENT COMMENT 'id',
    `user_id`         INTEGER      NOT NULL,
    `related_user_id` INTEGER      NOT NULL,
    `relation_type`   TINYINT      NOT NULL COMMENT '关系的类型：1厂家 2商家',
    `status`          TINYINT      NOT NULL COMMENT '绑定状态：0 待确认 1 正常 2 已取消',
    `name`            VARCHAR(255) NOT NULL COMMENT '备注名称',
    `created_by`      BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`      BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
) COMMENT ='用户绑定关系表';


CREATE TABLE `system_address`
(
    `id`          BIGINT AUTO_INCREMENT,
    `type`        TINYINT      NOT NULL DEFAULT 1 COMMENT '地址类型：1系统，2平台',
    `code`        BIGINT       NOT NULL COMMENT '地区邮编',
    `parent_code` BIGINT       NOT NULL COMMENT '父地区邮编',
    `name`        VARCHAR(255) NOT NULL COMMENT '地区名',
    `level`       TINYINT      NOT NULL COMMENT '地区层级',
    `system_code` BIGINT       NOT NULL DEFAULT 0 COMMENT '系统地区邮编',
    `created_at`  TIMESTAMP    NOT NULL,
    `updated_at`  TIMESTAMP    NOT NULL,
    `deleted_at`  TIMESTAMP    NULL,
    `created_by`  BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`  BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
);


create table orders
(
    id                   bigint unsigned auto_increment comment 'ID'
        primary key,
    user_id              int                         not null comment '用户id',
    shop_id              int                         not null comment '店铺id',
    factory_id           int            default 0    not null comment '厂家id',
    tid                  varchar(50)                 not null comment '主订单',
    type                 tinyint        default 0    not null comment '订单类型',
    express_no           varchar(255)                null comment '快递单号',
    template_id          int            default 0    not null comment '打印模板id',
    buyer_id             varchar(255)   default ''   null comment '买家id',
    buyer_nick           varchar(255)                null comment '买家昵称',
    seller_nick          varchar(50)                 null comment '卖家昵称',
    order_status         tinyint        default 0    not null comment '订单状态',
    refund_status        tinyint        default 0    not null comment '退款状态',
    print_status         tinyint        default 0    not null comment '打印状态',
    is_stockout_print    tinyint        default 0    not null comment '是否缺货打印 0否 1是',
    print_num            tinyint        default 0    not null comment '打印次数',
    printed_at           timestamp                   null comment '打印时间',
    print_shipping_at    timestamp                   null comment '发货单打印时间',
    print_tag_at         timestamp                   null comment '打印标签时间',
    shop_title           varchar(50)                 null comment '店铺名',
    receiver_state       varchar(50)                 null comment '收货人省份',
    receiver_city        varchar(50)                 null comment '收货人城市',
    receiver_district    varchar(50)                 null comment '收货人地区',
    receiver_town        varchar(50)                 null comment '收货人街道',
    receiver_name        varchar(30)                 null comment '收货人名字',
    receiver_phone       varchar(100)                null comment '收货人手机',
    receiver_zip         int                         null comment '收件人邮编',
    receiver_address     varchar(255)                null comment '收货地址',
    address_md5          char(64)                    null comment '收件人邮政编码',
    address_flag         tinyint        default 0    not null comment '地址标记',
    payment              decimal(10, 2) default 0.00 not null comment '实付金额',
    total_fee            decimal(10, 2) default 0.00 not null comment '总金额',
    discount_fee         decimal(10, 2) default 0.00 not null comment '优惠金额',
    post_fee             decimal(10, 2) default 0.00 not null comment '运费',
    seller_flag          varchar(20)    default '0'  null comment '卖家旗帜',
    seller_memo          text                        null comment '卖家备注',
    platform_memo        varchar(255)                null comment '平台备注',
    buyer_message        varchar(255)                null comment '买家留言',
    has_buyer_message    tinyint        default 0    not null comment '是否有买家留言',
    express_code         varchar(255)                null comment '快递公司代码',
    num                  int            default 0    not null comment '商品数量',
    sku_num              int            default 0    not null comment 'SKU数量',
    is_pre_sale          tinyint        default 0    not null comment '是否预售1是0否',
    village_flag         tinyint        default 0    not null comment '村子标记',
    goods_title          varchar(255)                null comment '商品标题',
    goods_title_last     varchar(255)                null comment '商品标题',
    sku_value            varchar(255)                null comment 'SKU的值',
    sku_value_last       varchar(255)                null comment 'SKU的值',
    merge_flag           varchar(32)    default ''   not null comment '合单标记',
    promise_ship_at      timestamp                   null comment '承诺发货时间',
    order_created_at     timestamp                   null comment '订单创建时间',
    order_updated_at     timestamp                   null comment '订单修改时间',
    send_at              timestamp                   null comment '发货时间',
    finished_at          timestamp                   null comment '订单完成时间',
    groupon_at           timestamp                   null comment '成团时间',
    locked_at            timestamp                   null comment '锁定时间',
    recycled_at          timestamp                   null comment '回收时间',
    pay_at               timestamp                   null comment '支付时间',
    is_comment           tinyint        default 0    not null comment '是否评价 (1:已评价)',
    smart_logistics      varchar(15)    default ''   not null comment '智能物流',
    district_code        bigint         default 0    not null comment '区域编码',
    custom_print_content varchar(255)   default ''   not null comment '自定义打印内容',
    custom_group         varchar(64)    default ''   not null comment '自定义分组',
    store_type           tinyint        default 0    not null comment '仓库类型',
    send_type            tinyint        default 0    not null comment '配送类型',
    created_at           timestamp                   null,
    updated_at           timestamp                   null,
    deleted_at           timestamp                   null,
    store_id             varchar(50)                 null comment '京东仓库编码',
    order_code           varchar(512)                null comment '订单代码，微信视频号用到',
    is_split             tinyint        default 0    not null comment '是否拆单 0否 1是',
    is_home_delivery     tinyint        default 0    not null comment '是否送货上门',
    is_live_order        tinyint        default 0    not null comment '是否直播订单',
    is_gift_order        tinyint        default 0    not null comment '是否赠品订单',
    urge_shipment_at     timestamp                   null comment '催发货时间',
    soft_remark          varchar(255)                null comment '软件备注',
    take_waybill_at      timestamp                   null comment '取号时间',
    pre_shipment_status  tinyint        default 0    not null comment '预发货状态',
    pre_shipment_at      timestamp                   null comment '预发货时间',
    is_remote_transit    tinyint        default 0    not null comment '是否为偏远中转订单 0否 1是',
    abnormal_type        tinyint        default 0    not null comment '异常类型',
    is_give_gift         tinyint        default 0    not null comment '是否赠送礼物',
    assign_type          varchar(15)    default ''   not null comment '分配类型',
    constraint orders_tid_unique
        unique (tid)
);

-- auto-generated definition
create table order_items
(
    id                      bigint unsigned auto_increment comment 'ID'
        primary key,
    user_id                 int                         not null comment '用户id',
    shop_id                 int                         not null comment '店铺id',
    order_id                bigint         default 0    not null comment '主订单主键',
    tid                     varchar(50)                 not null comment '主订单',
    oid                     varchar(50)                 not null comment '子订单号',
    type                    tinyint        default 0    not null comment '订单类型',
    payment                 decimal(10, 2) default 0.00 not null comment '实付金额',
    total_fee               decimal(10, 2) default 0.00 not null comment '总金额',
    discount_fee            decimal(10, 2) default 0.00 not null comment '优惠金额',
    goods_type              tinyint        default 1    not null comment '商品类型：1 普通，2 赠品',
    goods_price             decimal(10, 2) default 0.00 not null comment '商品单价',
    goods_pic               varchar(255)                null comment '商品图片',
    goods_title             varchar(255)                null comment '商品标题',
    goods_num               int            default 0    not null comment '商品数量',
    num_iid                 varchar(64)                 null comment '商品id',
    sku_id                  varchar(255)                null comment 'SKU id',
    sku_uuid                varchar(255)                null comment '商品SKU UUID,目前JD需要用',
    sku_value               varchar(255)   default '0'  not null comment 'SKU的值。如：手机套餐:官方标配',
    sku_value1              varchar(100)   default ''   not null comment 'SKU值1',
    sku_value2              varchar(100)   default ''   not null comment 'SKU值2',
    outer_iid               varchar(255)                null comment '商家外部商品编码',
    outer_sku_iid           varchar(255)                null comment '商家外部sku编码',
    refund_id               varchar(100)   default '0'  null comment '退款id',
    refund_status           tinyint        default 0    not null comment '退款状态',
    refund_sub_status       tinyint        default 0    not null,
    is_comment              tinyint        default 0    not null comment '是否评价 (1:已评价)',
    print_status            tinyint        default 0    not null comment '打印状态',
    print_num               int            default 0    not null comment '打印次数',
    refund_created_at       timestamp                   null comment '退款创建时间',
    refund_updated_at       timestamp                   null comment '退款修改时间',
    order_created_at        timestamp                   null comment '订单创建时间',
    order_updated_at        timestamp                   null comment '订单修改时间',
    created_at              timestamp                   null,
    updated_at              timestamp                   null,
    deleted_at              timestamp                   null,
    status                  tinyint        default 0    not null comment '订单状态',
    waybill_code            varchar(100)                null comment '运单号',
    send_at                 timestamp                   null comment '发货时间',
    first_send_at           datetime                    null comment '首次发货时间',
    first_send_waybill_code varchar(64)                 null comment '首次发货运单号',
    custom_order_sku_value  varchar(255)                null comment '自定义sku内容',
    product_no              varchar(255)   default ''   not null comment '货号',
    send_remain_num         int            default 0    not null comment '发货剩余数量',
    send_num                int            default 0    not null comment '已发货数量',
    pre_send_num            int unsigned   default '0'  not null comment '预发货数量',
    author_id               varchar(64)                 null comment '带货人id',
    author_name             varchar(64)                 null comment '带货人姓名',
    print_tag_at            timestamp                   null comment '打印标签时间',
    print_tag_num           int            default 0    not null comment '打印标签次数',
    is_pre_sale             tinyint        default 0    not null comment '是否预售',
    promise_ship_at         timestamp                   null comment '承诺发货时间',
    constraint order_items_oid_unique
        unique (oid)
);

create index order_items_order_id_index
    on order_items (order_id);

create index order_items_tid_index
    on order_items (tid);



CREATE TABLE `waybill_histories`
(
    `id`                  BIGINT AUTO_INCREMENT,
    `user_id`             INTEGER      NOT NULL COMMENT '用户id',
    `shop_id`             INTEGER      NOT NULL COMMENT '店铺id',
    `order_id`            BIGINT       NOT NULL COMMENT '订单表id',
    `order_no`            VARCHAR(200) NOT NULL COMMENT '订单编号',
    `package_id`          BIGINT       NOT NULL DEFAULT 0 COMMENT '包裹ID',
    `template_id`         INTEGER      NOT NULL DEFAULT 0 COMMENT '打印模板id',
    `auth_source`         TINYINT      NOT NULL DEFAULT 0 COMMENT '面单平台',
    `parent_waybill_code` VARCHAR(255) NOT NULL COMMENT '快运母单号',
    `waybill_code`        VARCHAR(255) NOT NULL COMMENT '电子面单号',
    `wp_code`             VARCHAR(64)  NOT NULL COMMENT '物流公司编码',
    `waybill_status`      TINYINT      NOT NULL DEFAULT 0 COMMENT '回收状态 0=未回收，1=已回收',
    `source`              TINYINT      NOT NULL DEFAULT 0 COMMENT '是否虚拟分享电子面单 0=不，1=是',
    `source_userid`       INTEGER      NOT NULL COMMENT '授权分享面单用户id',
    `source_shopid`       INTEGER      NOT NULL COMMENT '授权分享面单店铺id',
    `receiver_province`   VARCHAR(50)  NOT NULL COMMENT '收货人省份',
    `receiver_city`       VARCHAR(50)  NOT NULL COMMENT '收货人城市',
    `receiver_district`   VARCHAR(50)  NOT NULL COMMENT '收货人地区',
    `receiver_name`       VARCHAR(255) NOT NULL COMMENT '收货人名字',
    `receiver_phone`      VARCHAR(255) NOT NULL COMMENT '收货人手机',
    `receiver_address`    VARCHAR(255) NOT NULL COMMENT '收货地址',
    `print_data`          TEXT         NOT NULL COMMENT '打印的加密数据',
    `app_id`              VARCHAR(50)  NOT NULL COMMENT '礼品网appId',
    `created_at`          TIMESTAMP    NOT NULL,
    `updated_at`          TIMESTAMP    NOT NULL,
    `extra`               VARCHAR(255) NOT NULL COMMENT '附加信息',
    `print_data_items`    TEXT         NOT NULL COMMENT '打印商品信息',
    `order_type`          TINYINT      NOT NULL DEFAULT 1 COMMENT '订单类型 1普通订单 2自由打印 3代打订单',
    `name_index`          VARCHAR(100) NOT NULL COMMENT '姓名搜索索引',
    `phone_index`         VARCHAR(100) NOT NULL COMMENT '手机号搜索索引',
    `batch_no`            VARCHAR(255) NOT NULL COMMENT '取号批次号',
    `waybill_index`       INTEGER      NOT NULL DEFAULT 0 COMMENT '取号序号',
    `waybill_count`       INTEGER      NOT NULL DEFAULT 0 COMMENT '取号总数',
    `to_shop_id`          BIGINT       NOT NULL COMMENT '给哪个店铺取号',
    `outer_order_no`      VARCHAR(100) NOT NULL COMMENT '外部订单号',
    `platform_waybill_id` VARCHAR(255) NOT NULL COMMENT '平台运单ID',
    `version`             TINYINT      NOT NULL DEFAULT 0 COMMENT '版本号',
    `created_by`          VARCHAR(64)  NOT NULL COMMENT '创建人', -- This was already present, keeping the original definition.
    `updated_by`          VARCHAR(64)  NOT NULL COMMENT '更新人', -- This was already present, keeping the original definition.
    `sub_waybill_codes`   VARCHAR(255) NOT NULL COMMENT '子运单号',
    `soft_remark`         VARCHAR(255) NOT NULL COMMENT '软件备注',
    `send_content`        TEXT         NOT NULL COMMENT '发货内容',
    `company_id`          BIGINT       NOT NULL DEFAULT 0 COMMENT '网点ID',
    `removed_at`          TIMESTAMP    NOT NULL COMMENT '移除时间',
    PRIMARY KEY (`id`)
);


create index idx_companyid
    on waybill_histories (company_id);

create index idx_companyid_createdat
    on waybill_histories (company_id, created_at);

create index idx_createdat_shopid_waybillcode
    on waybill_histories (created_at, shop_id, waybill_code);

create index idx_createdat_toshopid_batchno
    on waybill_histories (created_at, to_shop_id, batch_no);

create index idx_shopid_authsource_createat
    on waybill_histories (shop_id, auth_source, created_at);

create index idx_shopid_createdat_waybill_code
    on waybill_histories (shop_id, created_at, waybill_code);

create index idx_shopid_package_id_createdat
    on waybill_histories (shop_id, package_id, created_at);

create index idx_shopid_waybillstatus_createdat
    on waybill_histories (shop_id, waybill_status, created_at);

create index idx_sourceshopid_shopid_wpcode_authsource_waybillstatus
    on waybill_histories (source_shopid, shop_id, wp_code, auth_source, waybill_status);

create index waybill_histories_batch_no_index
    on waybill_histories (batch_no);

create index waybill_histories_name_index_index
    on waybill_histories (name_index);

create index waybill_histories_order_id_index
    on waybill_histories (order_id);

create index waybill_histories_order_no_index
    on waybill_histories (order_no);

create index waybill_histories_package_id_index
    on waybill_histories (package_id);

create index waybill_histories_phone_index_index
    on waybill_histories (phone_index);

create index waybill_histories_source_shopid_created_at_index
    on waybill_histories (source_shopid, created_at);

create index waybill_histories_source_shopid_source_index
    on waybill_histories (source_shopid, source);

create index waybill_histories_to_shop_id_created_at_index
    on waybill_histories (to_shop_id, created_at);

create index waybill_histories_waybill_code_index
    on waybill_histories (waybill_code);

-- auto-generated definition
create table print_records
(
    id                  bigint unsigned auto_increment
        primary key,
    user_id             int                     not null comment '用户id',
    shop_id             int                     not null comment '店铺id',
    order_id            bigint       default 0  not null comment '订单表id',
    history_id          bigint       default 0  not null comment '取号记录表id',
    order_no            varchar(255)            null comment '订单编号',
    package_id          bigint       default 0  not null comment '包裹ID',
    parent_waybill_code varchar(255)            null comment '快运母单号',
    waybill_code        varchar(255)            null comment '电子面单号',
    wp_code             varchar(64)             null comment '物流公司编码',
    receiver_province   varchar(50)             null comment '收货人省份',
    receiver_city       varchar(50)             null comment '收货人城市',
    receiver_district   varchar(50)             null comment '收货人地区',
    receiver_town       varchar(50)             null comment '收货人街道',
    receiver_name       varchar(255)            null comment '收货人名字',
    receiver_phone      varchar(255)            null comment '收货人手机',
    receiver_zip        int                     null comment '收件人邮编',
    receiver_address    varchar(255)            null comment '收货地址',
    buyer_remark        varchar(255)            null comment '买家留言',
    app_id              varchar(50)  default '' not null comment '礼品网appId',
    created_at          timestamp               null,
    updated_at          timestamp               null,
    deleted_at          timestamp               null,
    print_data          text                    null comment '打印数据',
    batch_no            varchar(100)            null comment '批次号',
    name_index          varchar(100)            null comment '姓名搜索索引',
    phone_index         varchar(100)            null comment '手机号搜索索引',
    print_index         int          default 0  not null comment '打印序号',
    print_count         int          default 0  not null comment '打印总数',
    to_shop_id          bigint                  null comment '给哪个店铺打印',
    outer_order_no      varchar(100) default '' not null comment '外部订单号',
    template_id         int          default 0  not null comment '模板id',
    template_name       varchar(30)             null comment '模板名',
    version             tinyint      default 0  not null comment '版本号',
    created_by          varchar(64)             null comment '创建人',
    updated_by          varchar(64)             null comment '更新人',
    order_type          tinyint      default 1  not null comment '订单类型 1普通订单 2自由打印 3代打订单',
    send_content        text                    null comment '发货内容',
    company_id          bigint       default 0  not null comment '网点ID'
);

create index idx_shopid_batchno_waybillcode_created
    on print_records (shop_id, batch_no, waybill_code, created_at);

create index idx_shopid_createat_batchno_waybillcode_orderno
    on print_records (shop_id, created_at, batch_no, waybill_code, order_no);

create index idx_shopid_createdat_batchno_printindex
    on print_records (shop_id, created_at, batch_no, print_index);

create index print_records_created_at_index
    on print_records (created_at);

create index print_records_history_id_index
    on print_records (history_id);

create index print_records_name_index_index
    on print_records (name_index);

create index print_records_order_id_batch_no_index
    on print_records (order_id, batch_no);

create index print_records_order_no_index
    on print_records (order_no);

create index print_records_parent_waybill_code_index
    on print_records (parent_waybill_code);

create index print_records_phone_index_index
    on print_records (phone_index);

create index print_records_receiver_phone_index
    on print_records (receiver_phone);

create index print_records_to_shop_id_created_at_index
    on print_records (to_shop_id, created_at);

create index print_records_waybill_code_index
    on print_records (waybill_code);




CREATE TABLE `packages`
(
    `id`                              BIGINT AUTO_INCREMENT,
    `shop_id`                         INTEGER      NOT NULL COMMENT '店铺id',
    `print_status`                    TINYINT      NOT NULL DEFAULT 0 COMMENT '是否打印 0=未打印, 1=打印',
    `recycled_at`                     TIMESTAMP    NOT NULL COMMENT '回收时间',
    `waybill_code`                    VARCHAR(255) NOT NULL COMMENT '电子面单号',
    `tids`                            VARCHAR(255) NOT NULL COMMENT '订单好，多个订单拼接',
    `wp_code`                         VARCHAR(64)  NOT NULL COMMENT '物流公司编码',
    `template_id`                     INTEGER      NOT NULL DEFAULT 0 COMMENT '模板id',
    `auth_source`                     TINYINT      NOT NULL DEFAULT 0 COMMENT '面单平台',
    `batch_no`                        VARCHAR(255) NOT NULL COMMENT '批次好',
    `tid_oids`                        JSON         NOT NULL COMMENT 'tid oid关联',
    `created_at`                      TIMESTAMP    NOT NULL,
    `updated_at`                      TIMESTAMP    NOT NULL,
    `waybill_status`                  TINYINT      NOT NULL DEFAULT 0 COMMENT '取号状态 1取号中 2取号成功 3取号失败',
    `error_info`                      VARCHAR(150) NOT NULL COMMENT '错误原因',
    `sub_waybill_codes`               VARCHAR(255) NOT NULL COMMENT '子运单号',
    `goods_info`                      TEXT         NOT NULL COMMENT '包裹信息',
    `version`                         TINYINT      NOT NULL DEFAULT 0 COMMENT '版本号',
    `status`                          TINYINT      NOT NULL COMMENT '包裹状态 30:未发货 35:部分发货 40:已经发货 ',
    `print_order_status`              TINYINT      NOT NULL DEFAULT 0 COMMENT '打印订单状态 默认 30 未发货 40 已发货',
    `is_split`                        TINYINT      NOT NULL DEFAULT 0 COMMENT '是否拆分包裹 0否 1是',
    `take_waybill_at`                 TIMESTAMP    NOT NULL COMMENT '取号时间',
    `total_num`                       INTEGER      NOT NULL DEFAULT 0 COMMENT '总件数',
    `to_shop_id`                      BIGINT       NOT NULL DEFAULT 0 COMMENT '给哪个店铺取号',
    `pre_shipment_status`             TINYINT      NOT NULL DEFAULT 0 COMMENT '预发货状态 0不是预发货 1加入预发货',
    `operation_shop_id`               BIGINT       NOT NULL DEFAULT 0 COMMENT '操作店铺ID',
    `send_at`                         TIMESTAMP    NOT NULL COMMENT '发货时间',
    `print_at`                        TIMESTAMP    NOT NULL COMMENT '打印时间',
    `logistic_status`                 INTEGER      NOT NULL DEFAULT 0 COMMENT '物流状态',
    `promise_ship_time`               DATETIME     NOT NULL COMMENT '承诺发货时间',
    `logistic_delivery_time`          DATETIME     NOT NULL COMMENT '物流发货时间',
    `company_id`                      BIGINT       NOT NULL COMMENT '网点ID',
    `source_type`                     TINYINT      NOT NULL DEFAULT 0 COMMENT '来源类型 0:取号,1:内部发货',
    `delivery_type`                   TINYINT      NOT NULL DEFAULT 0 COMMENT '发货类型 0:无,1:首次发货,2:变更单号,3:补发,4:换货,99:其他',
    `send_scene`                      TINYINT      NOT NULL COMMENT '发货场景 1：一键发货 2：拆单发货',
    `waybill_wp_index`                VARCHAR(64)  NOT NULL COMMENT '快递单快递公司索引',
    `pre_shipment_at`                 DATETIME     NOT NULL COMMENT '预发货时间',
    `send_waybill_type`               TINYINT      NOT NULL DEFAULT 1 COMMENT '发货单号类型 1:普通单号 2:一单多包主包裹 3:一单多包从包裹',
    `multi_package_main_waybill_code` VARCHAR(32)  NOT NULL COMMENT '一单多包裹主单号',
    `print_product_data`              JSON         NOT NULL COMMENT '打印的货品数据',
    `created_by`                      BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`                      BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
);


CREATE INDEX `packages_batch_no_index`
    ON `packages` (`batch_no`); -- Corrected undefined to batch_no based on context
CREATE INDEX `packages_multi_package_main_waybill_code_index`
    ON `packages` (`multi_package_main_waybill_code`);
CREATE INDEX `packages_shop_id_created_at_index`
    ON `packages` (`shop_id`, `created_at`);
CREATE INDEX `packages_shop_id_send_at_index`
    ON `packages` (`shop_id`, `send_at`);
CREATE INDEX `packages_tids_index`
    ON `packages` (`tids`); -- Corrected undefined to tids based on context
CREATE INDEX `packages_to_shop_id_index`
    ON `packages` (`to_shop_id`);
CREATE INDEX `packages_waybill_code_index`
    ON `packages` (`waybill_code`); -- Corrected undefined to waybill_code based on context
CREATE INDEX `packages_waybill_wp_index_index`
    ON `packages` (`waybill_wp_index`);

CREATE TABLE `package_orders`
(
    `id`            BIGINT AUTO_INCREMENT,
    `order_id`      BIGINT      NOT NULL COMMENT '订单ID',
    `package_id`    BIGINT      NOT NULL COMMENT '包裹ID',
    `created_at`    TIMESTAMP   NOT NULL,
    `updated_at`    TIMESTAMP   NOT NULL,
    `order_item_id` BIGINT      NOT NULL COMMENT '订单商品ID',
    `num`           INTEGER     NOT NULL DEFAULT 0 COMMENT '商品数量',
    `version`       TINYINT     NOT NULL DEFAULT 0 COMMENT '版本号',
    `tid`           VARCHAR(50) NOT NULL COMMENT '主订单号',
    `oid`           VARCHAR(50) NOT NULL COMMENT '子订单号',
    `num_iid`       VARCHAR(50) NOT NULL COMMENT '商品编码',
    `sku_id`        VARCHAR(50) NOT NULL COMMENT 'Sku编码',
    `status`        TINYINT     NOT NULL COMMENT '包裹状态 30:未发货  40:已经发货 ',
    `source_type`   TINYINT     NOT NULL DEFAULT 0 COMMENT '来源类型 0:取号,1:内部发货',
    `delivery_type` TINYINT     NOT NULL DEFAULT 0 COMMENT '发货类型 0:无,1:首次发货,2:变更单号,3:补发,4:换货,99:其他',
    `created_by`    BIGINT      NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`    BIGINT      NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
);


CREATE INDEX `package_orders_order_id_index`
    ON `package_orders` (`order_id`);
CREATE INDEX `package_orders_order_item_id_index`
    ON `package_orders` (`order_item_id`);
CREATE INDEX `package_orders_package_id_index`
    ON `package_orders` (`package_id`);

CREATE TABLE `order_trace_list`
(
    `id`                BIGINT AUTO_INCREMENT,
    `user_id`           INTEGER      NOT NULL COMMENT '用户id',
    `shop_id`           INTEGER      NOT NULL COMMENT '店铺id',
    `type`              TINYINT      NOT NULL COMMENT '平台类型',
    `tid`               VARCHAR(50)  NOT NULL COMMENT '订单号',
    `express_code`      VARCHAR(50)  NOT NULL COMMENT '快递公司code',
    `express_no`        VARCHAR(50)  NOT NULL COMMENT '快递号',
    `status`            TINYINT      NOT NULL DEFAULT 0 COMMENT '物流状态(action对应)',
    `action`            VARCHAR(255) NOT NULL COMMENT '节点说明 ，指明当前节点揽收、派送，签收等',
    `receiver_province` VARCHAR(50)  NOT NULL COMMENT '收货人省份',
    `receiver_name`     VARCHAR(50)  NOT NULL COMMENT '收货人名字',
    `send_at`           TIMESTAMP    NOT NULL COMMENT '发货时间',
    `latest_updated_at` TIMESTAMP    NOT NULL COMMENT '最新轨迹更新时间',
    `latest_trace`      VARCHAR(255) NOT NULL COMMENT '最新轨迹',
    `trace_list`        TEXT         NOT NULL COMMENT '快件所有轨迹',
    `created_at`        TIMESTAMP    NOT NULL,
    `updated_at`        TIMESTAMP    NOT NULL,
    `auth_source`       TINYINT      NOT NULL COMMENT '面单平台',
    `created_by`        BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`        BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
);


CREATE INDEX `idx_latestupdatedat_status_createdat`
    ON `order_trace_list` (`latest_updated_at`, `status`, `created_at`);
CREATE INDEX `idx_sendat`
    ON `order_trace_list` (`send_at`);
CREATE INDEX `idx_shopid_sendat`
    ON `order_trace_list` (`shop_id`, `send_at`);
CREATE INDEX `idx_shopid_status_latestupdatedat`
    ON `order_trace_list` (`shop_id`, `status`, `latest_updated_at`);
CREATE INDEX `idx_shopid_status_sendat`
    ON `order_trace_list` (`shop_id`, `status`, `send_at`);
CREATE INDEX `idx_status_lastestupdatedat_updatedat`
    ON `order_trace_list` (`status`, `latest_updated_at`, `updated_at`);
CREATE INDEX `idx_updatedat_status_createat_lastestupdatedat`
    ON `order_trace_list` (`updated_at`, `status`, `created_at`, `latest_updated_at`);
CREATE INDEX `order_trace_list_express_no_index`
    ON `order_trace_list` (`express_no`);
CREATE INDEX `order_trace_list_tid_index`
    ON `order_trace_list` (`tid`);
CREATE INDEX `order_trace_list_type_index`
    ON `order_trace_list` (`type`);

-- auto-generated definition
create table companies
(
    id                  bigint unsigned auto_increment comment 'ID'
        primary key,
    user_id             int               not null comment '用户id',
    shop_id             int               not null comment '店铺id',
    auth_source         tinyint default 1 not null comment '授权来源',
    owner_id            varchar(255)      null comment 'owner_id',
    owner_name          varchar(255)      null comment 'owner_name',
    branch_name         varchar(255)      null comment '网点名称',
    branch_code         varchar(255)      null comment '网点Code',
    wp_code             varchar(64)       not null comment '快递公司ID',
    wp_name             varchar(64)       not null comment '快递公司名称',
    wp_type             tinyint default 0 not null comment '网站类型',
    status              tinyint default 0 not null comment '是否启用 0=不，1=是',
    source              tinyint default 0 not null comment '是否虚拟分享电子面单 0=不，1=是',
    source_userid       int               null comment '授权分享面单用户id',
    source_shopid       int               null comment '授权分享面单店铺id',
    source_status       tinyint default 0 not null comment '授权分享面单的操作状态 0=正常，1=冻结',
    quantity            int     default 0 not null comment '电子面单余额数量',
    cancel_quantity     int     default 0 not null comment '取消的面单总数',
    recycled_quantity   int     default 0 not null comment '已回收用面单数量',
    allocated_quantity  int     default 0 not null comment '已用面单数量',
    templates           text              not null comment '快递公司对于的模板信息json',
    province            varchar(255)      null comment '省',
    city                varchar(255)      null comment '市',
    district            varchar(255)      null comment '区',
    street              varchar(50)       null comment '街道',
    detail              varchar(255)      null comment '详细地址',
    created_at          timestamp         null,
    updated_at          timestamp         null,
    deleted_at          timestamp         null,
    settlement_code     varchar(20)       null comment '财务结算编码',
    platform_account_id varchar(50)       null comment '平台账号id',
    platform_shop_id    varchar(50)       null comment '平台店铺id',
    extended_info       varchar(512)      null comment '扩展信息',
    `created_by`        BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`        BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者'
);

create index companies_user_id_shop_id_auth_source_index
    on companies (user_id, shop_id, auth_source);

create index idx_shopId
    on companies (shop_id);



CREATE TABLE `goods`
(
    `id`               BIGINT AUTO_INCREMENT COMMENT 'id',
    `user_id`          INTEGER      NOT NULL COMMENT '用户id',
    `shop_id`          INTEGER      NOT NULL COMMENT '店铺id',
    `type`             TINYINT      NOT NULL DEFAULT 0 COMMENT '订单类型',
    `num_iid`          VARCHAR(64)  NOT NULL COMMENT '商品id',
    `outer_goods_id`   VARCHAR(64)  NOT NULL COMMENT '第三方商品编码',
    `goods_title`      VARCHAR(255) NOT NULL COMMENT '商品名称',
    `custom_title`     VARCHAR(255) NOT NULL COMMENT '自定义商品名称',
    `goods_pic`        VARCHAR(255) NOT NULL COMMENT '商品主图链接',
    `is_onsale`        TINYINT      NOT NULL DEFAULT 0 COMMENT '上下架状态',
    `goods_created_at` TIMESTAMP    NOT NULL COMMENT '平台创建时间',
    `goods_updated_at` TIMESTAMP    NOT NULL COMMENT '平台更新时间',
    `created_at`       TIMESTAMP    NOT NULL,
    `updated_at`       TIMESTAMP    NOT NULL,
    `deleted_at`       TIMESTAMP    NOT NULL,
    `flag`             VARCHAR(20)  NOT NULL COMMENT '旗标',
    `remark`           TEXT         NOT NULL COMMENT '备注',
    `created_by`       BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`       BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
);


CREATE INDEX `idx_numiid`
    ON `goods` (`num_iid`);
CREATE INDEX `idx_shopid_updatedat`
    ON `goods` (`shop_id`, `updated_at`);
CREATE INDEX `idx_shopid_withdata`
    ON `goods` (`shop_id`, `custom_title`, `num_iid`, `goods_title`, `outer_goods_id`, `deleted_at`);

CREATE TABLE `goods_skus`
(
    `id`               BIGINT AUTO_INCREMENT,
    `goods_id`         BIGINT       NOT NULL COMMENT '商品id',
    `user_id`          INTEGER      NOT NULL COMMENT '用户id',
    `shop_id`          INTEGER      NOT NULL COMMENT '店铺id',
    `type`             TINYINT      NOT NULL DEFAULT 0 COMMENT '订单类型',
    `sku_id`           VARCHAR(64)  NOT NULL COMMENT 'sku id',
    `sku_value`        VARCHAR(255) NOT NULL COMMENT '规格名称',
    `outer_id`         VARCHAR(64)  NOT NULL COMMENT '商家外部编码（sku）',
    `outer_goods_id`   VARCHAR(64)  NOT NULL COMMENT '商家外部编码（商品）',
    `custom_sku_value` VARCHAR(255) NOT NULL COMMENT '自定义商品名称',
    `sku_pic`          VARCHAR(255) NOT NULL COMMENT '商品主图链接',
    `is_onsale`        TINYINT      NOT NULL DEFAULT 0 COMMENT '上下架状态',
    `created_at`       TIMESTAMP    NOT NULL,
    `updated_at`       TIMESTAMP    NOT NULL,
    `deleted_at`       TIMESTAMP    NOT NULL,
    `created_by`       BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`       BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
);


CREATE INDEX `goods_skus_shop_id_custom_sku_value_index`
    ON `goods_skus` (`shop_id`, `custom_sku_value`);
CREATE INDEX `idx_goodsid_withdata_ext`
    ON `goods_skus` (`goods_id`, `sku_value`, `sku_id`, `custom_sku_value`, `outer_id`, `deleted_at`, `updated_at`);
CREATE INDEX `idx_shopid_updatedat`
    ON `goods_skus` (`shop_id`, `updated_at`);
CREATE INDEX `idx_shopid_withdata`
    ON `goods_skus` (`shop_id`, `sku_value`, `sku_id`, `custom_sku_value`, `outer_id`, `deleted_at`, `goods_id`);
CREATE INDEX `idx_skuid`
    ON `goods_skus` (`sku_id`);

CREATE TABLE `products`
(
    `id`         BIGINT       NOT NULL AUTO_INCREMENT COMMENT '商品ID',
    `name`       VARCHAR(255) NOT NULL COMMENT '货品名称',
    `product_no` VARCHAR(100) NOT NULL COMMENT '货号',
    `created_at` TIMESTAMP    NOT NULL COMMENT '创建时间',
    `updated_at` TIMESTAMP    NOT NULL COMMENT '更新时间',
    `created_by` BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by` BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
) COMMENT ='货品表';


CREATE TABLE `product_skus`
(
    `id`             BIGINT       NOT NULL AUTO_INCREMENT COMMENT '规格ID',
    `product_id`     BIGINT       NOT NULL COMMENT '货品ID (关联products表)',
    `image_url`      VARCHAR(255) NOT NULL DEFAULT '' COMMENT '规格图片URL', -- Changed NULL to '' (empty string) for default
    `merchant_code`   VARCHAR(100) NOT NULL COMMENT '商家编码',
    `sku_name`       VARCHAR(100) NOT NULL DEFAULT '' COMMENT '规格名称',    -- Changed NULL to '' (empty string) for default
    `sku_short_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '规格简称',    -- Changed NULL to '' (empty string) for default
    `barcode`        VARCHAR(100) NOT NULL DEFAULT '' COMMENT '条形码',      -- Changed NULL to '' (empty string) for default
    `weight`         DECIMAL      NOT NULL DEFAULT 0 COMMENT '重量 (kg)',
    `volume`         DECIMAL      NOT NULL DEFAULT 0 COMMENT '体积 (m³)',
    `cost_price`     DECIMAL      NOT NULL DEFAULT 0 COMMENT '成本 (元)',
    `selling_price`  DECIMAL      NOT NULL DEFAULT 0 COMMENT '价格 (元)',
    `created_at`     TIMESTAMP    NOT NULL COMMENT '创建时间',
    `updated_at`     TIMESTAMP    NOT NULL COMMENT '更新时间',
    `created_by`     BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`     BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
) COMMENT ='货品规格表';


CREATE TABLE `shipping_addresses`
(
    `id`          BIGINT AUTO_INCREMENT COMMENT 'ID',
    `user_id`     INTEGER      NOT NULL COMMENT '用户id',
    `shop_id`     INTEGER      NOT NULL COMMENT '店铺id',
    `sender_name` VARCHAR(128) NOT NULL COMMENT '寄件人',
    `mobile`      VARCHAR(11)  NOT NULL COMMENT '手机号码',
    `tel`         VARCHAR(50)  NOT NULL COMMENT '电话号码',
    `postal_code` VARCHAR(32)  NOT NULL COMMENT '邮编编码',
    `province`    VARCHAR(32)  NOT NULL COMMENT '省',
    `city`        VARCHAR(32)  NOT NULL COMMENT '市',
    `district`    VARCHAR(32)  NOT NULL COMMENT '区',
    `address`     VARCHAR(32)  NOT NULL COMMENT '详细地址',
    `is_default`  TINYINT      NOT NULL DEFAULT 0 COMMENT '默认地址 0=不是，1=是',
    `tip`         TINYINT      NOT NULL DEFAULT 0 COMMENT '默认是发件人还是收件人 0=是发件人，1=是收件人',
    `created_at`  TIMESTAMP    NOT NULL,
    `updated_at`  TIMESTAMP    NOT NULL,
    `deleted_at`  TIMESTAMP    NOT NULL,
    `created_by`  BIGINT       NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`  BIGINT       NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
);


CREATE INDEX `idx_shopid`
    ON `shipping_addresses` (`shop_id`);
CREATE INDEX `shipping_addresses_user_id_shop_id_index`
    ON `shipping_addresses` (`user_id`, `shop_id`);

CREATE TABLE `goods_product_relations`
(
    `id`             BIGINT AUTO_INCREMENT,
    `user_id`        BIGINT    NOT NULL,
    `goods_id`       BIGINT    NOT NULL,
    `goods_sku_id`   BIGINT    NOT NULL,
    `product_id`     BIGINT    NOT NULL,
    `product_sku_id` BIGINT    NOT NULL,
    `created_at`     TIMESTAMP NOT NULL,
    `updated_at`     TIMESTAMP NOT NULL,
    `deleted_at`     TIMESTAMP NOT NULL,
    `created_by`     BIGINT    NOT NULL DEFAULT 0 COMMENT '创建者',
    `updated_by`     BIGINT    NOT NULL DEFAULT 0 COMMENT '更新者',
    PRIMARY KEY (`id`)
) COMMENT ='商品和货品的关联';