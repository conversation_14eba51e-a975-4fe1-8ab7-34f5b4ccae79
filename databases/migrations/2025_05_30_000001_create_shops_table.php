<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shops', function (Blueprint $table) {
            $table->comment('店铺表');
            $table->bigIncrements('id')->comment('授权记录 ID');
            $table->integer('user_id')->comment('用户id, user主键');
            $table->tinyInteger('type')->default(0)->comment('授权类型,如taoabo');
            $table->tinyInteger('role_type')->default(1)->comment('店铺角色类型 0未知 1商家 2厂家');
            $table->string('identifier', 128)->comment('唯一身份')->index();
            $table->string('access_token', 500)->nullable()->comment('授权token')->index();
            $table->string('refresh_token', 500)->nullable()->comment('刷新token');
            $table->timestamp('expire_at')->nullable()->comment('access token过期时间');
            $table->timestamp('auth_at')->nullable()->comment('授权开始时间')->index();
            $table->tinyInteger('auth_status')->default(0)->comment('授权状态');
            $table->integer('login_count')->default(0)->comment('登录次数');
            $table->string('shop_name', 100)->nullable()->comment('店铺名');
            $table->string('shop_identifier', 100)->nullable()->comment('多店铺唯一身份');
            $table->string('shop_logo', 300)->nullable()->comment('店铺LOGO');
            $table->string('name', 100)->nullable()->comment('用户名');
            $table->string('auth_user_id', 64)->nullable()->comment('授权用户id')->index();
            $table->string('last_sync_page', 255)->nullable()->comment('同步的页码');
            $table->tinyInteger('sync_switch')->default(1)->comment('同步开关');
            $table->timestamp('last_sync_at')->nullable()->comment('最后同步订单时间');
            $table->timestamp('last_operated_at')->nullable()->comment('最后手动同步操作时间');
            $table->timestamp('last_factory_sync_at')->nullable()->comment('最后厂商同步时间');
            $table->timestamp('last_factory_operated_at')->nullable()->comment('最后厂商手动同步时间');
            $table->timestamp('last_goods_sync_at')->nullable()->comment('最后商品同步时间');
            $table->timestamp('last_refund_sync_at')->nullable()->comment('最后同步退款订单时间');
            $table->integer('original_user_id')->default(0)->comment('原来的用户ID');
            $table->integer('inviter')->default(0)->comment('邀请绑定用户id')->index();
            $table->string('service_id', 100)->nullable()->comment('微信小店使用')->index();
            $table->string('specification_id', 100)->nullable()->comment('微信小店使用');
            $table->string('shop_code', 50)->nullable()->comment('店铺唯一标识')->index();
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->softDeletes();
            
            // 添加索引
            $table->index('user_id');
            $table->index('type');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shops');
    }
};
