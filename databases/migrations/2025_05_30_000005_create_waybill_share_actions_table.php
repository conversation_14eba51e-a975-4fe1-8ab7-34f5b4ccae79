<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('waybill_share_actions', function (Blueprint $table) {
            $table->comment('面单共享操作表');
            $table->integer('id')->autoIncrement();
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('shop_id');
            $table->integer('company_id')->comment('快递公司id');
            $table->string('shop_name', 255)->comment('店铺名');
            $table->string('name', 100)->comment('用户名');
            $table->string('identifier', 128)->comment('唯一身份');
            $table->string('branch_name', 255)->comment('网点名称');
            $table->string('wp_code', 64)->comment('快递公司ID');
            $table->string('wp_name', 64)->comment('快递公司名称');
            $table->integer('balance_limit')->default(0)->comment('单号数量');
            $table->string('province', 255)->comment('省');
            $table->string('city', 255)->comment('市');
            $table->string('district', 255)->comment('区');
            $table->string('street', 50)->comment('街道');
            $table->string('detail', 255)->comment('详细地址');
            $table->tinyInteger('action')->default(4)->comment('操作状态，0是恢复，1是冻结，2是删除，3是追加，4是新建，5是减少');
            $table->tinyInteger('auth_source')->comment('面单账号来源');
            $table->bigInteger('dest_shop_id')->comment('目标店铺id,就是接受面单的店铺id');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            
            // 添加索引
            $table->index(['user_id', 'shop_id'], 'waybill_share_actions_user_id_shop_id_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('waybill_share_actions');
    }
};
