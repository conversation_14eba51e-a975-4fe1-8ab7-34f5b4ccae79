<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOrderItemRelationsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_item_relations', function (Blueprint $table) {
            $table->comment('订单商品关系表');
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->comment('用户 id');
            $table->bigInteger('order_id')->comment('订单id');
            $table->bigInteger('order_item_id')->comment('订单商品id');
            $table->bigInteger('assigner_user_id')->default(0)->comment('分配人ID');
            $table->string('system_order_no', 50)->comment('系统单号');
            $table->tinyInteger('source_type')->default(1)->comment('来源类型 1订单 2代打 3拿货');
            $table->timestamps();
            $table->softDeletes();

            // 索引
            $table->index('user_id', 'order_item_relations_user_id_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_item_relations');
    }
}
