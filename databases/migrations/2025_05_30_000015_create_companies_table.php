<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->comment('公司表');
            $table->bigIncrements('id')->unsigned()->comment('ID');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('auth_source')->default(1)->comment('授权来源');
            $table->string('owner_id', 255)->nullable()->comment('owner_id');
            $table->string('owner_name', 255)->nullable()->comment('owner_name');
            $table->string('branch_name', 255)->nullable()->comment('网点名称');
            $table->string('branch_code', 255)->nullable()->comment('网点Code');
            $table->string('wp_code', 64)->comment('快递公司ID');
            $table->string('wp_name', 64)->comment('快递公司名称');
            $table->tinyInteger('wp_type')->default(0)->comment('网站类型');
            $table->tinyInteger('status')->default(0)->comment('是否启用 0=不，1=是');
            $table->tinyInteger('source')->default(0)->comment('是否虚拟分享电子面单 0=不，1=是');
            $table->integer('source_userid')->nullable()->comment('授权分享面单用户id');
            $table->integer('source_shopid')->nullable()->comment('授权分享面单店铺id');
            $table->tinyInteger('source_status')->default(0)->comment('授权分享面单的操作状态 0=正常，1=冻结');
            $table->integer('quantity')->default(0)->comment('电子面单余额数量');
            $table->integer('cancel_quantity')->default(0)->comment('取消的面单总数');
            $table->integer('recycled_quantity')->default(0)->comment('已回收用面单数量');
            $table->integer('allocated_quantity')->default(0)->comment('已用面单数量');
            $table->text('templates')->comment('快递公司对于的模板信息json');
            $table->string('province', 255)->nullable()->comment('省');
            $table->string('city', 255)->nullable()->comment('市');
            $table->string('district', 255)->nullable()->comment('区');
            $table->string('street', 50)->nullable()->comment('街道');
            $table->string('detail', 255)->nullable()->comment('详细地址');
            $table->string('settlement_code', 20)->nullable()->comment('财务结算编码');
            $table->string('platform_account_id', 50)->nullable()->comment('平台账号id');
            $table->string('platform_shop_id', 50)->nullable()->comment('平台店铺id');
            $table->string('extended_info', 512)->nullable()->comment('扩展信息');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->softDeletes();
            
            // 添加索引
            $table->index(['user_id', 'shop_id', 'auth_source']);
            $table->index('shop_id', 'idx_shopId');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
