<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shop_sync_status', function (Blueprint $table) {
            $table->comment('店铺同步状态');
            $table->bigIncrements('id')->comment('主键');
            $table->bigInteger('shop_id')->comment('店铺id');
            $table->integer('sync_status')->default(0)->comment('同步状态，0 正常 2 禁用');
            $table->string('sync_type', 255)->default('after_sale')->comment('同步类型');
            $table->dateTime('manual_time', 3)->nullable()->comment('手动触发时间');
            $table->dateTime('auto_time', 3)->nullable()->comment('自动触发时间');
            $table->dateTime('data_end_time', 3)->nullable()->comment('数据结束时间');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shop_sync_status');
    }
};
