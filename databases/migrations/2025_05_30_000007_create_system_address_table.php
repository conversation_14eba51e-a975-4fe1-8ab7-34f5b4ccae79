<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_address', function (Blueprint $table) {
            $table->comment('系统地址表');
            $table->bigIncrements('id');
            $table->tinyInteger('type')->default(1)->comment('地址类型：1系统，2平台');
            $table->bigInteger('code')->comment('地区邮编');
            $table->bigInteger('parent_code')->comment('父地区邮编');
            $table->string('name', 255)->comment('地区名');
            $table->tinyInteger('level')->comment('地区层级');
            $table->bigInteger('system_code')->default(0)->comment('系统地区邮编');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_address');
    }
};
