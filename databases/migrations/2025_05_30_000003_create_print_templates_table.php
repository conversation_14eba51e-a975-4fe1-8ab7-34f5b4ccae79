<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('print_templates', function (Blueprint $table) {
            $table->comment('打印模板表');
            $table->bigIncrements('id');
            $table->integer('user_id')->comment('用户id');
            $table->tinyInteger('style')->default(1)->comment('模板样式 1=拼多多标准模板，3=拼多多一联单模板');
            $table->string('name', 128)->comment('模板名称');
            $table->decimal('width')->comment('宽');
            $table->decimal('height')->comment('高');
            $table->string('waybill_type', 100)->comment('面单类型');
            $table->tinyInteger('show_logo')->default(0)->comment('快递公司logo, 0-不显示 1 显示');
            $table->double('horizontal')->default(0)->comment('水平偏移量');
            $table->integer('horizontal_type')->default(0)->comment('水平偏移类型（1：左 2：右）');
            $table->double('vertical')->default(0)->comment('垂直偏移量');
            $table->integer('vertical_type')->default(0)->comment('垂直偏移类型（1：上 2：下）');
            $table->text('merge_template_url')->comment('合并模板地址');
            $table->string('wp_code', 64)->comment('快递公司ID');
            $table->string('wp_name', 64)->comment('快递公司名称');
            $table->text('custom_config')->comment('商家自定义模板信息');
            $table->text('print_contents')->comment('模板内容');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('print_templates');
    }
};
