<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_skus', function (Blueprint $table) {
            $table->comment('货品规格表');
            $table->bigIncrements('id')->comment('规格ID');
            $table->bigInteger('product_id')->comment('货品ID (关联products表)');
            $table->bigInteger('user_id')->comment('用户id');
            $table->string('image_url', 255)->default('')->comment('规格图片URL');
            $table->string('merchant_code', 100)->comment('商家编码');
            $table->string('sku_name', 100)->default('')->comment('规格名称');
            $table->string('sku_short_name', 100)->default('')->comment('规格简称');
            $table->string('barcode', 100)->default('')->comment('条形码');
            $table->decimal('weight')->default(0)->comment('重量 (kg)');
            $table->decimal('volume')->default(0)->comment('体积 (m³)');
            $table->decimal('cost_price')->default(0)->comment('成本 (元)');
            $table->decimal('selling_price')->default(0)->comment('价格 (元)');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();

            // 添加索引
            $table->index('product_id');
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_skus');
    }
};
