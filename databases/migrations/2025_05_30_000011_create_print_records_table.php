<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('print_records', function (Blueprint $table) {
            $table->comment('打印记录表');
            $table->bigIncrements('id')->unsigned();
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->bigInteger('order_id')->default(0)->comment('订单表id');
            $table->bigInteger('history_id')->default(0)->comment('取号记录表id');
            $table->string('order_no', 255)->nullable()->comment('订单编号');
            $table->bigInteger('package_id')->default(0)->comment('包裹ID');
            $table->string('parent_waybill_code', 255)->nullable()->comment('快运母单号');
            $table->string('waybill_code', 255)->nullable()->comment('电子面单号');
            $table->string('wp_code', 64)->nullable()->comment('物流公司编码');
            $table->string('receiver_province', 50)->nullable()->comment('收货人省份');
            $table->string('receiver_city', 50)->nullable()->comment('收货人城市');
            $table->string('receiver_district', 50)->nullable()->comment('收货人地区');
            $table->string('receiver_town', 50)->nullable()->comment('收货人街道');
            $table->string('receiver_name', 255)->nullable()->comment('收货人名字');
            $table->string('receiver_phone', 255)->nullable()->comment('收货人手机');
            $table->integer('receiver_zip')->nullable()->comment('收件人邮编');
            $table->string('receiver_address', 255)->nullable()->comment('收货地址');
            $table->string('buyer_remark', 255)->nullable()->comment('买家留言');
            $table->string('app_id', 50)->default('')->comment('礼品网appId');
            $table->text('print_data')->nullable()->comment('打印数据');
            $table->string('batch_no', 100)->nullable()->comment('批次号');
            $table->string('name_index', 100)->nullable()->comment('姓名搜索索引');
            $table->string('phone_index', 100)->nullable()->comment('手机号搜索索引');
            $table->integer('print_index')->default(0)->comment('打印序号');
            $table->integer('print_count')->default(0)->comment('打印总数');
            $table->bigInteger('to_shop_id')->nullable()->comment('给哪个店铺打印');
            $table->string('outer_order_no', 100)->default('')->comment('外部订单号');
            $table->integer('template_id')->default(0)->comment('模板id');
            $table->string('template_name', 30)->nullable()->comment('模板名');
            $table->tinyInteger('version')->default(0)->comment('版本号');
            $table->string('created_by', 64)->nullable()->comment('创建人');
            $table->string('updated_by', 64)->nullable()->comment('更新人');
            $table->tinyInteger('order_type')->default(1)->comment('订单类型 1普通订单 2自由打印 3代打订单');
            $table->text('send_content')->nullable()->comment('发货内容');
            $table->bigInteger('company_id')->default(0)->comment('网点ID');
            $table->timestamps();
            $table->softDeletes();
            
            // 添加索引
            $table->index(['shop_id', 'batch_no', 'waybill_code', 'created_at'], 'idx_shopid_batchno_waybillcode_created');
            $table->index(['shop_id', 'created_at', 'batch_no', 'waybill_code', 'order_no'], 'idx_shopid_createat_batchno_waybillcode_orderno');
            $table->index(['shop_id', 'created_at', 'batch_no', 'print_index'], 'idx_shopid_createdat_batchno_printindex');
            $table->index('created_at');
            $table->index('history_id');
            $table->index('name_index');
            $table->index(['order_id', 'batch_no']);
            $table->index('order_no');
            $table->index('parent_waybill_code');
            $table->index('phone_index');
            $table->index('receiver_phone');
            $table->index(['to_shop_id', 'created_at']);
            $table->index('waybill_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('print_records');
    }
};
