<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pick_task_codes', function (Blueprint $table) {
            $table->comment('拿货编码表');
            $table->bigIncrements('id')->comment('主键');
            $table->bigInteger('pick_task_id')->comment('拿货任务id');
            $table->tinyInteger('status')->default(0)->comment('拿货状态 0:未拿货 1:已拿货 2:已取消');
            $table->string('code', 100)->comment('拿货编码');
            $table->timestamp('scanned_at')->nullable()->comment('扫描时间');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->softDeletes();
            
            // 添加索引
            $table->index('pick_task_id');
            $table->index('code');
            $table->unique(['pick_task_id', 'code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pickup_codes');
    }
};