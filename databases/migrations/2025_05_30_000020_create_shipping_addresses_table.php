<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_addresses', function (Blueprint $table) {
            $table->comment('发货地址表');
            $table->bigIncrements('id')->comment('ID');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->string('sender_name', 128)->comment('寄件人');
            $table->string('mobile', 11)->comment('手机号码');
            $table->string('tel', 50)->comment('电话号码');
            $table->string('postal_code', 32)->comment('邮编编码');
            $table->string('province', 32)->comment('省');
            $table->string('city', 32)->comment('市');
            $table->string('district', 32)->comment('区');
            $table->string('address', 32)->comment('详细地址');
            $table->tinyInteger('is_default')->default(0)->comment('默认地址 0=不是，1=是');
            $table->tinyInteger('tip')->default(0)->comment('默认是发件人还是收件人 0=是发件人，1=是收件人');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_addresses');
    }
};
