<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('waybill_histories', function (Blueprint $table) {
            $table->comment('面单历史表');
            $table->bigIncrements('id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->bigInteger('order_id')->comment('订单表id');
            $table->string('order_no', 200)->comment('订单编号');
            $table->bigInteger('package_id')->default(0)->comment('包裹ID');
            $table->integer('template_id')->default(0)->comment('打印模板id');
            $table->tinyInteger('auth_source')->default(0)->comment('面单平台');
            $table->string('parent_waybill_code', 255)->comment('快运母单号');
            $table->string('waybill_code', 255)->comment('电子面单号');
            $table->string('wp_code', 64)->comment('物流公司编码');
            $table->tinyInteger('waybill_status')->default(0)->comment('回收状态 0=未回收，1=已回收');
            $table->tinyInteger('source')->default(0)->comment('是否虚拟分享电子面单 0=不，1=是');
            $table->integer('source_userid')->comment('授权分享面单用户id');
            $table->integer('source_shopid')->comment('授权分享面单店铺id');
            $table->string('receiver_province', 50)->comment('收货人省份');
            $table->string('receiver_city', 50)->comment('收货人城市');
            $table->string('receiver_district', 50)->comment('收货人地区');
            $table->string('receiver_name', 255)->comment('收货人名字');
            $table->string('receiver_phone', 255)->comment('收货人手机');
            $table->string('receiver_address', 255)->comment('收货地址');
            $table->text('print_data')->comment('打印的加密数据');
            $table->string('app_id', 50)->comment('礼品网appId');
            $table->string('extra', 255)->comment('附加信息');
            $table->text('print_data_items')->comment('打印商品信息');
            $table->tinyInteger('order_type')->default(1)->comment('订单类型 1普通订单 2自由打印 3代打订单');
            $table->string('name_index', 100)->comment('姓名搜索索引');
            $table->string('phone_index', 100)->comment('手机号搜索索引');
            $table->string('batch_no', 255)->comment('取号批次号');
            $table->integer('waybill_index')->default(0)->comment('取号序号');
            $table->integer('waybill_count')->default(0)->comment('取号总数');
            $table->bigInteger('to_shop_id')->comment('给哪个店铺取号');
            $table->string('outer_order_no', 100)->comment('外部订单号');
            $table->string('platform_waybill_id', 255)->comment('平台运单ID');
            $table->tinyInteger('version')->default(0)->comment('版本号');
            $table->string('created_by', 64)->comment('创建人');
            $table->string('updated_by', 64)->comment('更新人');
            $table->string('sub_waybill_codes', 255)->comment('子运单号');
            $table->string('soft_remark', 255)->comment('软件备注');
            $table->text('send_content')->comment('发货内容');
            $table->bigInteger('company_id')->default(0)->comment('网点ID');
            $table->timestamp('removed_at')->comment('移除时间');
            $table->timestamps();
            
            // 添加索引
            $table->index('company_id', 'idx_companyid');
            $table->index(['company_id', 'created_at'], 'idx_companyid_createdat');
            $table->index(['created_at', 'shop_id', 'waybill_code'], 'idx_createdat_shopid_waybillcode');
            $table->index(['created_at', 'to_shop_id', 'batch_no'], 'idx_createdat_toshopid_batchno');
            $table->index(['shop_id', 'auth_source', 'created_at'], 'idx_shopid_authsource_createat');
            $table->index(['shop_id', 'created_at', 'waybill_code'], 'idx_shopid_createdat_waybill_code');
            $table->index(['shop_id', 'package_id', 'created_at'], 'idx_shopid_package_id_createdat');
            $table->index(['shop_id', 'waybill_status', 'created_at'], 'idx_shopid_waybillstatus_createdat');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('waybill_histories');
    }
};
