<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('package_orders', function (Blueprint $table) {
            $table->comment('包裹订单表');
            $table->bigIncrements('id');
            $table->bigInteger('order_id')->comment('订单ID');
            $table->bigInteger('package_id')->comment('包裹ID');
            $table->bigInteger('order_item_id')->comment('订单商品ID');
            $table->integer('num')->default(0)->comment('商品数量');
            $table->tinyInteger('version')->default(0)->comment('版本号');
            $table->string('tid', 50)->comment('主订单号');
            $table->string('oid', 50)->comment('子订单号');
            $table->string('num_iid', 50)->comment('商品编码');
            $table->string('sku_id', 50)->comment('Sku编码');
            $table->tinyInteger('status')->comment('包裹状态 30:未发货  40:已经发货 ');
            $table->tinyInteger('source_type')->default(0)->comment('来源类型 0:取号,1:内部发货');
            $table->tinyInteger('delivery_type')->default(0)->comment('发货类型 0:无,1:首次发货,2:变更单号,3:补发,4:换货,99:其他');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            
            // 添加索引
            $table->index('order_id');
            $table->index('order_item_id');
            $table->index('package_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('package_orders');
    }
};
