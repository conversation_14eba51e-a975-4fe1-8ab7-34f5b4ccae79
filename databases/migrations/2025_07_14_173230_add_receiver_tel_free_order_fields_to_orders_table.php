<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // 添加 receiver_tel 字段，放到 receiver_phone 字段后面
            $table->string('receiver_tel', 32)->nullable()->comment('收货人电话')->after('receiver_phone');

            // 添加 free_order_source 字段，放到 order_biz_type 字段后面
            $table->tinyInteger('free_order_source')->default(0)->comment('自由打印订单来源')->after('order_biz_type');

            // 添加 free_order_no 字段，放到 tid 字段后面
            $table->string('free_order_no', 50)->nullable()->comment('自由打印订单编号')->after('tid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['receiver_tel', 'free_order_source', 'free_order_no']);
        });
    }
};
