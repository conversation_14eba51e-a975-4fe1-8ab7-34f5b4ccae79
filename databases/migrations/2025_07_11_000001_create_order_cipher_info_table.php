<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_cipher_info', function (Blueprint $table) {
            $table->comment('订单加密信息表');
            $table->bigIncrements('id')->comment('主键');
            $table->bigInteger('order_id')->comment('订单id');
            $table->string('oaid', 1024)->nullable()->comment('订单加密id');
            $table->string('receiver_phone_mask', 50)->nullable()->comment('收货人手机号码脱敏');
            $table->string('receiver_name_mask', 50)->nullable()->comment('收货人姓名脱敏');
            $table->string('receiver_address_mask', 100)->nullable()->comment('收货人地址脱敏');
            $table->string('receiver_phone_ciphertext', 1024)->nullable()->comment('收货人手机号码密文');
            $table->string('receiver_name_ciphertext', 1024)->nullable()->comment('收货人姓名密文');
            $table->string('receiver_address_ciphertext', 1024)->nullable()->comment('收货人地址密文');
            $table->timestamps();
            $table->timestamp('deleted_at')->nullable();
            
            // 添加唯一约束
            $table->unique('order_id', 'order_cipher_info_order_id_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_cipher_info');
    }
};
