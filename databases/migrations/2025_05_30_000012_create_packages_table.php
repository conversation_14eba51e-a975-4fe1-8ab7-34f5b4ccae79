<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->comment('包裹表');
            $table->bigIncrements('id');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('print_status')->default(0)->comment('是否打印 0=未打印, 1=打印');
            $table->timestamp('recycled_at')->comment('回收时间');
            $table->string('waybill_code', 255)->comment('电子面单号');
            $table->string('tids', 255)->comment('订单好，多个订单拼接');
            $table->string('wp_code', 64)->comment('物流公司编码');
            $table->integer('template_id')->default(0)->comment('模板id');
            $table->tinyInteger('auth_source')->default(0)->comment('面单平台');
            $table->string('batch_no', 255)->comment('批次好');
            $table->json('tid_oids')->comment('tid oid关联');
            $table->tinyInteger('waybill_status')->default(0)->comment('取号状态 1取号中 2取号成功 3取号失败');
            $table->string('error_info', 150)->comment('错误原因');
            $table->string('sub_waybill_codes', 255)->comment('子运单号');
            $table->text('goods_info')->comment('包裹信息');
            $table->tinyInteger('version')->default(0)->comment('版本号');
            $table->tinyInteger('status')->comment('包裹状态 30:未发货 35:部分发货 40:已经发货 ');
            $table->tinyInteger('print_order_status')->default(0)->comment('打印订单状态 默认 30 未发货 40 已发货');
            $table->tinyInteger('is_split')->default(0)->comment('是否拆分包裹 0否 1是');
            $table->timestamp('take_waybill_at')->comment('取号时间');
            $table->integer('total_num')->default(0)->comment('总件数');
            $table->bigInteger('to_shop_id')->default(0)->comment('给哪个店铺取号');
            $table->tinyInteger('pre_shipment_status')->default(0)->comment('预发货状态 0不是预发货 1加入预发货');
            $table->bigInteger('operation_shop_id')->default(0)->comment('操作店铺ID');
            $table->timestamp('send_at')->comment('发货时间');
            $table->timestamp('print_at')->comment('打印时间');
            $table->integer('logistic_status')->default(0)->comment('物流状态');
            $table->dateTime('promise_ship_time')->comment('承诺发货时间');
            $table->dateTime('logistic_delivery_time')->comment('物流发货时间');
            $table->bigInteger('company_id')->comment('网点ID');
            $table->tinyInteger('source_type')->default(0)->comment('来源类型 0:取号,1:内部发货');
            $table->tinyInteger('delivery_type')->default(0)->comment('发货类型 0:无,1:首次发货,2:变更单号,3:补发,4:换货,99:其他');
            $table->tinyInteger('send_scene')->comment('发货场景 1：一键发货 2：拆单发货');
            $table->string('waybill_wp_index', 64)->comment('快递单快递公司索引');
            $table->dateTime('pre_shipment_at')->comment('预发货时间');
            $table->tinyInteger('send_waybill_type')->default(1)->comment('发货单号类型 1:普通单号 2:一单多包主包裹 3:一单多包从包裹');
            $table->string('multi_package_main_waybill_code', 32)->comment('一单多包裹主单号');
            $table->json('print_product_data')->comment('打印的货品数据');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            
            // 添加索引
            $table->index('batch_no');
            $table->index('multi_package_main_waybill_code');
            $table->index(['shop_id', 'created_at']);
            $table->index(['shop_id', 'send_at']);
            $table->index('tids');
            $table->index('to_shop_id');
            $table->index('waybill_code');
            $table->index('waybill_wp_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('packages');
    }
};
