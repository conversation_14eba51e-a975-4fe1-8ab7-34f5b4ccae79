<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('goods_product_relations', function (Blueprint $table) {
            $table->string('goods_sku_sku_id', 64)->nullable()->after('goods_sku_id')->comment('平台的sku id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('goods_product_relations', function (Blueprint $table) {
            $table->dropColumn('goods_sku_sku_id');
        });
    }
};
