<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->decimal('weight', 10, 2)->nullable()->change()->comment('重量');
            $table->decimal('volume', 10, 2)->nullable()->change()->comment('体积');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->integer('weight')->nullable()->change()->comment('重量');
            $table->integer('volume')->nullable()->change()->comment('体积');
        });
    }
};
