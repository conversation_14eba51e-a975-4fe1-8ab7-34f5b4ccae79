<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('queue_task_info', function (Blueprint $table) {
            $table->comment('队列任务信息表');
            $table->bigIncrements('id')->comment('主键');
            $table->bigInteger('shop_id')->default(0)->comment('店铺id');
            $table->string('batch_no', 40)->comment('批次号');
            $table->tinyInteger('task_status')->default(0)->comment('任务状态 -1:待执行 1:成功 2:错误 3:处理中 0：失败');
            $table->text('task_desc')->nullable()->comment('任务状态描述');
            $table->json('task_param')->nullable()->comment('任务参数');
            $table->integer('total')->nullable()->comment('总数');
            $table->integer('success')->nullable()->comment('成功数量');
            $table->integer('failed')->nullable()->comment('失败数量');
            $table->integer('created_by')->default(0)->nullable()->comment('创建人');
            $table->integer('updated_by')->default(0)->nullable()->comment('修改人');
            $table->timestamps();

            // 添加索引
            $table->index('shop_id');
            $table->index('batch_no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('queue_task_info');
    }
};
