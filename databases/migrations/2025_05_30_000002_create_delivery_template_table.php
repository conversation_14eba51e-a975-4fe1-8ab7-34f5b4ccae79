<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_template', function (Blueprint $table) {
            $table->comment('配送模板表');
            $table->bigIncrements('id');
            $table->bigInteger('user_id')->default(0)->comment('团队id');
            $table->string('size_type', 100)->comment('尺寸 例如A4（210*297）');
            $table->string('template_name', 100)->comment('模板名称');
            $table->double('width')->comment('宽');
            $table->double('height')->comment('高');
            $table->json('content')->comment('模板内容');
            $table->string('created_by', 20)->comment('创建人');
            $table->string('updated_by', 20)->comment('修改人');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_template');
    }
};
