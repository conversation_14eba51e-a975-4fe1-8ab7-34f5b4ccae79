<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('goods_skus', function (Blueprint $table) {
            $table->comment('商品SKU表');
            $table->bigIncrements('id');
            $table->bigInteger('goods_id')->comment('商品id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('type')->default(0)->comment('订单类型');
            $table->string('sku_id', 64)->comment('sku id');
            $table->string('sku_value', 255)->comment('规格名称');
            $table->string('outer_id', 64)->comment('商家外部编码（sku）');
            $table->string('outer_goods_id', 64)->comment('商家外部编码（商品）');
            $table->string('custom_sku_value', 255)->comment('自定义商品名称');
            $table->string('sku_pic', 255)->comment('商品主图链接');
            $table->tinyInteger('is_onsale')->default(0)->comment('上下架状态');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->timestamp('deleted_at')->nullable();
            
            // 添加索引
            $table->index(['shop_id', 'custom_sku_value']);
            $table->index(['goods_id', 'sku_value', 'sku_id', 'custom_sku_value', 'outer_id', 'deleted_at', 'updated_at'], 'idx_goodsid_withdata_ext');
            $table->index(['shop_id', 'updated_at'], 'idx_shopid_updatedat');
            $table->index(['shop_id', 'sku_value', 'sku_id', 'custom_sku_value', 'outer_id', 'deleted_at', 'goods_id'], 'idx_shopid_withdata');
            $table->index('sku_id', 'idx_skuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('goods_skus');
    }
};
