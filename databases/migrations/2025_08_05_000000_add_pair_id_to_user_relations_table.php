<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AddPairIdToUserRelationsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_relations', function (Blueprint $table) {
            $table->unsignedBigInteger('pair_id')->after('id')->comment('配对ID，用于标识互为关系的两条记录');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_relations', function (Blueprint $table) {
            $table->dropColumn('pair_id');
        });
    }
}