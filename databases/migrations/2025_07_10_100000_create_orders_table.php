<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOrdersTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->comment('订单表');
            $table->bigIncrements('id');
            $table->bigInteger('shop_id')->unsigned()->default(0)->comment('店铺id');
            $table->string('tid', 50)->comment('订单号');
            $table->tinyInteger('type')->default(0)->comment('平台类型');
            $table->tinyInteger('order_type')->default(0)->comment('订单类型 1平台订单 2自由打印');
            $table->tinyInteger('order_biz_type')->default(0)->comment('订单业务类型');
            $table->string('waybill_code', 32)->nullable()->comment('面单号');
            $table->string('union_wp_code', 20)->nullable()->comment('物流公司编码');
            $table->string('buyer_id', 128)->nullable()->comment('买家id');
            $table->string('buyer_nick', 32)->nullable()->comment('买家昵称');
            $table->string('seller_nick', 32)->nullable()->comment('卖家昵称');
            $table->string('shop_title', 32)->nullable()->comment('店铺标题');
            $table->decimal('payment', 10, 2)->nullable()->comment('支付金额');
            $table->decimal('total_fee', 10, 2)->nullable()->comment('订单金额');
            $table->decimal('discount_fee', 10, 2)->nullable()->comment('优惠金额');
            $table->decimal('post_fee', 10, 2)->nullable()->comment('邮费');
            $table->decimal('collection_fee', 10, 2)->default(0.00)->nullable()->comment('代收货款');
            $table->string('custom_group', 20)->nullable()->comment('自定义分组');
            $table->tinyInteger('order_status')->default(0)->comment('订单状态');
            $table->tinyInteger('after_sales_status')->default(0)->comment('售后状态');
            $table->tinyInteger('print_status')->default(0)->comment('打印状态');
            $table->tinyInteger('print_num')->default(0)->comment('打印次数');
            $table->dateTime('printed_at')->nullable()->comment('打印时间');
            $table->dateTime('print_shipping_at')->nullable()->comment('打印发货时间');
            $table->string('receiver_province', 50)->nullable()->comment('收货人省');
            $table->string('receiver_city', 50)->nullable()->comment('收货人市');
            $table->string('receiver_district', 50)->nullable()->comment('收货人区');
            $table->string('receiver_town', 50)->nullable()->comment('收货人镇');
            $table->string('receiver_name', 64)->nullable()->comment('收货人姓名');
            $table->string('receiver_phone', 32)->nullable()->comment('收货人手机');
            $table->integer('receiver_zip')->default(0)->comment('收货人邮编');
            $table->string('receiver_address', 128)->nullable()->comment('收货人地址');
            $table->integer('sender_address_id')->nullable()->comment('发件人地址ID,这个关联到发货地址管理里面的ID');
            $table->string('sender_name', 32)->nullable()->comment('发件人');
            $table->string('sender_province', 32)->nullable()->comment('发件人省');
            $table->string('sender_city', 32)->nullable()->comment('发件人市');
            $table->string('sender_district', 32)->nullable()->comment('发件人区');
            $table->string('sender_town', 32)->nullable()->comment('发件人街道');
            $table->string('sender_zip', 10)->nullable()->comment('发件人邮编');
            $table->string('sender_address', 128)->nullable()->comment('发件人详细地址');
            $table->string('sender_phone', 64)->nullable()->comment('发件人手机');
            $table->string('sender_tel', 64)->nullable()->comment('发件人电话');
            $table->bigInteger('district_code')->default(0)->comment('区域编码');
            $table->string('address_md5', 64)->nullable()->comment('地址md5');
            $table->tinyInteger('address_flag')->default(0)->comment('地址标识');
            $table->tinyInteger('address_village_flag')->default(0)->comment('地址村标识');
            $table->string('seller_flag', 20)->default('')->comment('卖家旗帜');
            $table->string('seller_memo', 255)->nullable()->comment('卖家备注');
            $table->string('platform_memo', 255)->nullable()->comment('平台备注');
            $table->string('buyer_message', 255)->nullable()->comment('买家留言');
            $table->tinyInteger('has_buyer_message')->default(0)->comment('是否有买家留言');
            $table->tinyInteger('is_comment')->default(0)->comment('是否评价');
            $table->integer('goods_total_num')->default(0)->comment('商品总数量');
            $table->integer('sku_num')->default(0)->comment('sku数量');
            $table->tinyInteger('is_pre_sale')->default(0)->comment('是否预售');
            $table->string('merge_flag', 64)->default('')->nullable()->comment('合并标识');
            $table->string('smart_logistics', 20)->nullable()->comment('智能物流');
            $table->string('store_id', 20)->nullable()->comment('门店id');
            $table->string('custom_print_content', 255)->nullable()->comment('自定义打印内容');
            $table->string('platform_logistics', 20)->nullable()->comment('平台指定物流');
            $table->string('software_logistics', 20)->nullable()->comment('软件指定物流');
            $table->timestamp('urge_delivery_at')->nullable()->comment('催发货时间');
            $table->dateTime('recycled_at')->nullable()->comment('回收时间');
            $table->dateTime('promise_ship_at')->nullable()->comment('承诺发货时间');
            $table->dateTime('send_at')->nullable()->comment('发货时间');
            $table->dateTime('confirm_at')->nullable()->comment('确认收货时间');
            $table->dateTime('locked_at')->nullable()->comment('锁定时间');
            $table->dateTime('pay_at')->nullable()->comment('付款时间');
            $table->timestamp('group_at')->nullable()->comment('成团时间');
            $table->timestamp('modify_address_at')->nullable()->comment('修改收货地址时间');
            $table->dateTime('order_created_at')->nullable()->comment('订单创建时间');
            $table->dateTime('order_updated_at')->nullable()->comment('订单更新时间');
            $table->timestamps();
            $table->softDeletes();

            // 索引
            $table->unique('tid');
            $table->index(['address_md5', 'shop_id', 'order_status', 'pay_at', 'after_sales_status', 'send_at', 'locked_at', 'printed_at'], 'idx_addressmd5_withdata');
            $table->index(['district_code', 'shop_id'], 'orders_district_code_shop_id_index');
            $table->index(['merge_flag', 'shop_id'], 'orders_merge_flag_shop_id_index');
            $table->index(['receiver_phone', 'shop_id'], 'orders_receiver_phone_shop_id_index');
            $table->index(['shop_id', 'group_at'], 'orders_shop_id_group_at_index');
            $table->index(['shop_id', 'order_biz_type', 'print_status'], 'orders_shop_id_order_biz_type_print_status_index');
            $table->index(['waybill_code', 'shop_id'], 'orders_waybill_code_shop_id_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
}
