<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_trace_list', function (Blueprint $table) {
            $table->comment('订单物流跟踪表');
            $table->bigIncrements('id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('type')->comment('平台类型');
            $table->string('tid', 50)->comment('订单号');
            $table->string('express_code', 50)->comment('快递公司code');
            $table->string('express_no', 50)->comment('快递号');
            $table->tinyInteger('status')->default(0)->comment('物流状态(action对应)');
            $table->string('action', 255)->comment('节点说明 ，指明当前节点揽收、派送，签收等');
            $table->string('receiver_province', 50)->comment('收货人省份');
            $table->string('receiver_name', 50)->comment('收货人名字');
            $table->timestamp('send_at')->comment('发货时间');
            $table->timestamp('latest_updated_at')->comment('最新轨迹更新时间');
            $table->string('latest_trace', 255)->comment('最新轨迹');
            $table->text('trace_list')->comment('快件所有轨迹');
            $table->tinyInteger('auth_source')->comment('面单平台');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            
            // 添加索引
            $table->index(['latest_updated_at', 'status', 'created_at'], 'idx_latestupdatedat_status_createdat');
            $table->index('send_at', 'idx_sendat');
            $table->index(['shop_id', 'send_at'], 'idx_shopid_sendat');
            $table->index(['shop_id', 'status', 'latest_updated_at'], 'idx_shopid_status_latestupdatedat');
            $table->index(['shop_id', 'status', 'send_at'], 'idx_shopid_status_sendat');
            $table->index(['status', 'latest_updated_at', 'updated_at'], 'idx_status_lastestupdatedat_updatedat');
            $table->index(['updated_at', 'status', 'created_at', 'latest_updated_at'], 'idx_updatedat_status_createat_lastestupdatedat');
            $table->index('express_no');
            $table->index('tid');
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_trace_list');
    }
};
