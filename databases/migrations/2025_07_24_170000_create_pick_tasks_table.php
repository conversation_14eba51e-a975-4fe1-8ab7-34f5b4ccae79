<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pick_tasks', function (Blueprint $table) {
            $table->comment('拿货任务表');
            $table->bigIncrements('id')->comment('主键');
            $table->bigInteger('shop_id')->default(0)->comment('店铺id');
            $table->bigInteger('order_id')->default(0)->comment('订单id');
            $table->bigInteger('order_item_id')->default(0)->comment('订单商品id');
            $table->bigInteger('picker_id')->default(0)->comment('拿货员id');
            $table->bigInteger('product_id')->default(0)->comment('货品id');
            $table->bigInteger('product_sku_id')->default(0)->comment('货品sku_id');
            $table->bigInteger('supplier_id')->default(0)->comment('供应商id');
            $table->integer('total_quantity')->default(0)->comment('拿货总件数');
            $table->integer('picked_quantity')->default(0)->comment('已拿件数');
            $table->decimal('pick_price', 10, 2)->default(0)->comment('拿货价');
            $table->tinyInteger('pickup_status')->default(0)->comment('拿货状态 0:待指派 1:正在拿货 2:部分拿货 3:已拿货 4:停止拿货 5:缺货 6:停产 7:已关闭');
            $table->tinyInteger('assign_status')->default(0)->comment('指派状态 0:未指派 1:已指派');
            $table->tinyInteger('code_print_status')->default(0)->comment('编码打印状态 0:未打印 1:已打印');
            $table->tinyInteger('take_no_status')->default(0)->comment('取号状态 0:未取号 1:已取号');
            $table->tinyInteger('express_print_status')->default(0)->comment('快递单打印状态 0:未打印 1:已打印');
            $table->tinyInteger('payment_status')->default(0)->comment('支付状态 0:未支付 1:已支付');
            $table->tinyInteger('push_print_status')->default(0)->comment('推送档口打印状态 0:未推送 1:推送已打印 2:推送未打印');
            $table->timestamp('push_print_at')->nullable()->comment('推送档口打印时间');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->softDeletes();
            
            // 添加索引
            $table->index(['shop_id', 'pickup_status']);
            $table->index(['order_id']);
            $table->index(['order_item_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pick_tasks');
    }
};