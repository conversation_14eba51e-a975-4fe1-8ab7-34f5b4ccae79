<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('goods_product_relations', function (Blueprint $table) {
            $table->comment('商品和货品的关联');
            $table->bigIncrements('id');
            $table->bigInteger('user_id');
            $table->tinyInteger('bind_type')->default(0)->comment('绑定类型 1商品关联 2货源关联');
            $table->bigInteger('goods_id');
            $table->bigInteger('goods_sku_id');
            $table->bigInteger('product_id');
            $table->bigInteger('product_sku_id');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->softDeletes();
            
            // 添加索引
            $table->index(['user_id', 'bind_type']);
            $table->index('goods_id');
            $table->index('product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('goods_product_relations');
    }
};
