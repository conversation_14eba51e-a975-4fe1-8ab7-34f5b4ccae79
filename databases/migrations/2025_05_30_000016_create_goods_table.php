<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('goods', function (Blueprint $table) {
            $table->comment('商品表');
            $table->bigIncrements('id')->comment('id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('type')->default(0)->comment('订单类型');
            $table->string('num_iid', 64)->comment('商品id');
            $table->string('outer_goods_id', 64)->comment('第三方商品编码');
            $table->string('goods_title', 255)->comment('商品名称');
            $table->string('custom_title', 255)->comment('自定义商品名称');
            $table->string('goods_pic', 255)->comment('商品主图链接');
            $table->tinyInteger('is_onsale')->default(0)->comment('上下架状态');
            $table->timestamp('goods_created_at')->comment('平台创建时间');
            $table->timestamp('goods_updated_at')->comment('平台更新时间');
            $table->string('flag', 20)->comment('旗标');
            $table->text('remark')->comment('备注');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->timestamp('deleted_at')->nullable();
            
            // 添加索引
            $table->index('num_iid', 'idx_numiid');
            $table->index(['shop_id', 'updated_at'], 'idx_shopid_updatedat');
            $table->index(['shop_id', 'custom_title', 'num_iid', 'goods_title', 'outer_goods_id', 'deleted_at'], 'idx_shopid_withdata');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('goods');
    }
};
