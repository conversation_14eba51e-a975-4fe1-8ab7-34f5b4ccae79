<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('waybills', function (Blueprint $table) {
            $table->comment('电子面单表');
            $table->bigIncrements('id')->unsigned()->comment('ID');
            $table->integer('user_id')->comment('用户id, user主键');
            $table->integer('shop_id')->comment('店铺id');
            $table->tinyInteger('auth_source')->default(0)->comment('授权来源');
            $table->string('access_token', 500)->comment('电子面单授权token');
            $table->string('refresh_token', 500)->comment('电子面单刷新token');
            $table->string('owner_id', 255)->comment('owner_id');
            $table->string('owner_name', 255)->comment('owner_name');
            $table->integer('expires_in')->comment('token有效期');
            $table->timestamp('expires_at')->nullable()->comment('token过期时间');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
            $table->softDeletes();
            
            // 添加索引
            $table->index(['shop_id', 'auth_source'], 'idx_shopid_authsource');
            $table->index(['user_id', 'shop_id', 'auth_source']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('waybills');
    }
};
