<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOrderItemsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->comment('订单商品表');
            $table->bigIncrements('id');
            $table->bigInteger('order_id')->default(0)->comment('订单id');
            $table->bigInteger('shop_id')->default(0)->comment('店铺id');
            $table->string('tid', 50)->comment('订单编号');
            $table->string('oid', 50)->comment('子订单编号');
            $table->bigInteger('product_id')->default(0)->comment('货品id');
            $table->bigInteger('goods_id')->default(0)->comment('商品id');
            $table->tinyInteger('orders_status')->default(0)->comment('订单状态');
            $table->tinyInteger('print_status')->default(0)->comment('打印状态');
            $table->string('waybill_code', 32)->nullable()->comment('面单号');
            $table->string('wp_code', 50)->nullable()->comment('物流公司编码');
            $table->decimal('payment', 10, 2)->default(0.00)->comment('实付金额');
            $table->decimal('total_fee', 10, 2)->default(0.00)->comment('订单金额');
            $table->decimal('discount_fee', 10, 2)->default(0.00)->comment('优惠金额');
            $table->decimal('sku_price', 10, 2)->default(0.00)->comment('商品单价');
            $table->string('sku_pic', 255)->nullable()->comment('商品图片');
            $table->string('goods_title', 256)->nullable()->comment('商品标题');
            $table->integer('sku_num')->default(0)->comment('商品数量');
            $table->string('num_iid', 20)->nullable()->comment('商品id');
            $table->string('sku_id', 50)->nullable()->comment('商品SKU id');
            $table->string('sku_value', 100)->nullable()->comment('sku值');
            $table->string('sku_value1', 100)->default('')->comment('SKU值1');
            $table->string('sku_value2', 100)->default('')->comment('SKU值2');
            $table->string('outer_iid', 50)->nullable()->comment('商家外部商品编码');
            $table->string('outer_sku_iid', 50)->nullable()->comment('商家外部sku编码');
            $table->integer('weight')->nullable()->comment('重量');
            $table->integer('volume')->nullable()->comment('体积');
            $table->string('after_sale_no', 50)->nullable()->comment('售后单号');
            $table->tinyInteger('after_sales_status')->default(0)->comment('售后状态');
            $table->tinyInteger('is_comment')->default(0)->comment('是否评价');
            $table->integer('print_num')->default(0)->comment('打印次数');
            $table->string('quality_order_code', 50)->nullable()->comment('质检单号');
            $table->string('custom_order_sku_value', 50)->nullable()->comment('自定义商品属性');
            $table->string('pt_product_no', 50)->nullable()->comment('平台商品编码');
            $table->string('author_id', 64)->nullable()->comment('带货人id');
            $table->string('author_name', 64)->nullable()->comment('带货人姓名');
            $table->tinyInteger('quality_status')->default(0)->comment('质检状态');
            $table->tinyInteger('delivery_type')->default(1)->comment('配送类型：普通 1,京配 天猫仓 同城等');
            $table->tinyInteger('is_gift')->default(0)->comment('是否赠品');
            $table->tinyInteger('is_pre_sale')->default(0)->comment('是否预售');
            $table->timestamp('promise_ship_at')->nullable()->comment('承诺发货时间');
            $table->timestamp('promise_delivery_at')->nullable()->comment('承诺送达时间');
            $table->timestamp('modify_product_at')->nullable()->comment('修改商品时间');
            $table->timestamp('locked_at')->nullable()->comment('锁定时间');
            $table->tinyInteger('assign_status')->default(0)->comment('分配状态 0未分配 1已分配');
            $table->dateTime('after_sale_created_at')->nullable()->comment('售后创建时间');
            $table->dateTime('after_sale_updated_at')->nullable()->comment('售后更新时间');
            $table->dateTime('send_at')->nullable()->comment('发货时间');
            $table->dateTime('order_created_at')->nullable()->comment('订单创建时间');
            $table->dateTime('order_updated_at')->nullable()->comment('订单更新时间');
            $table->timestamps();
            $table->softDeletes();
            $table->tinyInteger('waybill_status')->default(0)->comment('取号状态0 未取号 1 已取号');
            $table->dateTime('printed_at')->nullable()->comment('打印时间');
            $table->tinyInteger('logistic_deliver')->default(0)->nullable()->comment('预发货状态，0未预发货，1已预发货');

            // 索引
            $table->unique('oid');
            $table->index('order_id', 'order_items_order_id_index');
            $table->index('tid', 'order_items_tid_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
}
