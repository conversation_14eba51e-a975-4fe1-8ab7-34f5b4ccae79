<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_relations', function (Blueprint $table) {
            $table->comment('用户绑定关系表');
            $table->bigIncrements('id')->comment('id');
            $table->integer('user_id');
            $table->integer('related_user_id');
            $table->tinyInteger('relation_type')->comment('关系的类型：1厂家 2商家');
            $table->tinyInteger('status')->comment('绑定状态：0 待确认 1 正常 2 已取消');
            $table->string('name', 255)->comment('备注名称');
            $table->bigInteger('created_by')->default(0)->comment('创建者');
            $table->bigInteger('updated_by')->default(0)->comment('更新者');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_relations');
    }
};
