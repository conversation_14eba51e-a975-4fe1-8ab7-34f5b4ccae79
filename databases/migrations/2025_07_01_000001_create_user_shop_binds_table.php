<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_shop_binds', function (Blueprint $table) {
            $table->comment('用户店铺绑定表');
            $table->bigIncrements('id')->comment('主键ID');
            $table->bigInteger('user_id')->unsigned()->comment('用户ID');
            $table->bigInteger('shop_id')->unsigned()->comment('店铺ID');
            $table->timestamps();
            $table->softDeletes();
            
            // 添加索引
            $table->index('user_id', 'idx_user_shop_binds_user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_shop_binds');
    }
};
