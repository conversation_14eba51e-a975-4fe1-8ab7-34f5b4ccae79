<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_item_relations', function (Blueprint $table) {
            $table->tinyInteger('split_merge_flag')->default(0)->comment('拆单合单标记 0 默认 1手动拆单 2手动合单')->after('source_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_item_relations', function (Blueprint $table) {
            $table->dropColumn('split_merge_flag');
        });
    }
};