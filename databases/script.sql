create table attachment
(
    id           bigint unsigned auto_increment comment '主键'
        primary key,
    storage_mode varchar(20)  default 'local' not null comment '存储模式:local=本地,oss=阿里云,qiniu=七牛云,cos=腾讯云',
    origin_name  varchar(255)                 null comment '原文件名',
    object_name  varchar(50)                  null comment '新文件名',
    hash         varchar(64)                  null comment '文件hash',
    mime_type    varchar(255)                 null comment '资源类型',
    storage_path varchar(100)                 null comment '存储目录',
    suffix       varchar(20)                  null comment '文件后缀',
    size_byte    bigint                       null comment '字节数',
    size_info    varchar(50)                  null comment '文件大小',
    url          varchar(255)                 null comment 'url地址',
    created_by   bigint       default 0       not null comment '创建者',
    updated_by   bigint       default 0       not null comment '更新者',
    created_at   datetime                     null,
    updated_at   datetime                     null,
    remark       varchar(255) default ''      not null comment '备注',
    constraint attachment_hash_unique
        unique (hash)
)
    comment '上传文件信息表';

create index attachment_storage_path_index
    on attachment (storage_path);

create table code_generator
(
    id                  bigint unsigned auto_increment
        primary key,
    plan_name           varchar(255)                   not null comment '方案名称',
    table_name          varchar(255)                   not null comment '表名称',
    fields              json                           not null comment '字段列表',
    package_name        varchar(255)                   null comment '子模块名称',
    database_connection varchar(255) default 'default' not null comment '数据库连接',
    menu_name           varchar(255)                   not null comment '菜单名称',
    menu_id             varchar(255)                   not null comment '菜单标识',
    menu_parent_id      bigint       default 0         not null comment '父级菜单ID',
    remark              varchar(255)                   null comment '备注信息',
    created_at          datetime                       null,
    updated_at          datetime                       null
);

create table crontab
(
    id               bigint unsigned auto_increment
        primary key,
    name             varchar(30)       not null,
    status           tinyint default 0 not null,
    is_on_one_server tinyint default 0 not null,
    is_singleton     tinyint default 0 not null,
    memo             varchar(60)       not null,
    type             varchar(10)       not null,
    rule             varchar(30)       not null,
    value            text              not null,
    created_at       datetime          null,
    updated_at       datetime          null
);

create table crontab_execute_log
(
    id             bigint unsigned auto_increment
        primary key,
    crontab_id     bigint            not null,
    name           varchar(100)      not null,
    status         tinyint default 0 not null,
    target         varchar(200)      not null,
    exception_info text              not null,
    created_at     datetime          null,
    updated_at     datetime          null
);

create table dictionary
(
    id         bigint unsigned auto_increment comment '主键'
        primary key,
    type_id    bigint             not null comment '字典类型',
    code       varchar(32)        not null comment '字典编码',
    value      varchar(128)       not null comment '字典值',
    label      varchar(32)        not null comment '字典标签',
    i18n       varchar(64)        null comment '国际化',
    i18n_scope tinyint  default 1 not null comment '国际化类型:1=全局,2=本地',
    color      varchar(16)        null comment '文字颜色',
    sort       smallint default 0 not null comment '排序',
    status     tinyint  default 1 not null comment '状态:1=正常,2=停用',
    remark     varchar(255)       null comment '备注信息',
    created_by bigint   default 0 not null comment '创建者',
    updated_by bigint   default 0 not null comment '更新者',
    created_at datetime           null,
    updated_at datetime           null
)
    comment '字典表';

create index dictionary_code_index
    on dictionary (code);

create index dictionary_id_index
    on dictionary (id);

create table dictionary_type
(
    id         bigint unsigned auto_increment comment '主键'
        primary key,
    name       varchar(32)       not null comment '分类名称',
    code       varchar(32)       not null comment '分类编码',
    status     tinyint default 1 not null comment '状态:1=正常,2=停用',
    remark     varchar(255)      null comment '备注信息',
    created_by bigint  default 0 not null comment '创建者',
    updated_by bigint  default 0 not null comment '更新者',
    created_at datetime          null,
    updated_at datetime          null,
    constraint dictionary_type_code_unique
        unique (code)
)
    comment '字典分类表';

create index dictionary_type_id_index
    on dictionary_type (id);

create table menu
(
    id         bigint unsigned auto_increment comment '主键'
        primary key,
    parent_id  bigint unsigned         not null comment '父ID',
    name       varchar(50)  default '' not null comment '菜单名称',
    meta       json                    null comment '附加属性',
    path       varchar(60)  default '' not null comment '路径',
    component  varchar(150) default '' not null comment '组件路径',
    redirect   varchar(100) default '' not null comment '重定向地址',
    status     tinyint      default 1  not null comment '状态:1=正常,2=停用',
    sort       smallint     default 0  not null comment '排序',
    created_by bigint       default 0  not null comment '创建者',
    updated_by bigint       default 0  not null comment '更新者',
    created_at datetime                null,
    updated_at datetime                null,
    remark     varchar(60)  default '' not null comment '备注',
    constraint menu_name_unique
        unique (name)
)
    comment '菜单信息表';

create table migrations
(
    id        int unsigned auto_increment
        primary key,
    migration varchar(255) not null,
    batch     int          not null
);

create table role
(
    id         bigint unsigned auto_increment comment '主键'
        primary key,
    name       varchar(30)             not null comment '角色名称',
    code       varchar(100)            not null comment '角色代码',
    status     tinyint      default 1  not null comment '状态:1=正常,2=停用',
    sort       smallint     default 0  not null comment '排序',
    created_by bigint       default 0  not null comment '创建者',
    updated_by bigint       default 0  not null comment '更新者',
    created_at datetime                null,
    updated_at datetime                null,
    remark     varchar(255) default '' not null comment '备注',
    constraint role_code_unique
        unique (code)
)
    comment '角色信息表';

create table role_belongs_menu
(
    id         bigint unsigned auto_increment
        primary key,
    role_id    bigint   not null comment '角色id',
    menu_id    bigint   not null comment '菜单id',
    created_at datetime null,
    updated_at datetime null
);

create table rules
(
    id         bigint unsigned auto_increment
        primary key,
    ptype      varchar(255) null,
    v0         varchar(255) null,
    v1         varchar(255) null,
    v2         varchar(255) null,
    v3         varchar(255) null,
    v4         varchar(255) null,
    v5         varchar(255) null,
    created_at timestamp    null,
    updated_at timestamp    null
);

create table user
(
    id              bigint unsigned auto_increment comment '用户ID,主键'
        primary key,
    username        varchar(20)                            not null comment '用户名',
    password        varchar(100)                           not null comment '密码',
    user_type       varchar(3)   default '100'             not null comment '用户类型:100=系统用户',
    nickname        varchar(30)  default ''                not null comment '用户昵称',
    phone           varchar(11)  default ''                not null comment '手机',
    email           varchar(50)  default ''                not null comment '用户邮箱',
    avatar          varchar(255) default ''                not null comment '用户头像',
    signed          varchar(255) default ''                not null comment '个人签名',
    status          tinyint      default 1                 not null comment '状态:1=正常,2=停用',
    login_ip        varchar(45)  default '127.0.0.1'       not null comment '最后登陆IP',
    login_time      timestamp    default CURRENT_TIMESTAMP not null comment '最后登陆时间',
    backend_setting json                                   null comment '后台设置数据',
    created_by      bigint       default 0                 not null comment '创建者',
    updated_by      bigint       default 0                 not null comment '更新者',
    created_at      datetime                               null,
    updated_at      datetime                               null,
    remark          varchar(255) default ''                not null comment '备注',
    constraint user_username_unique
        unique (username)
)
    comment '用户信息表';

create table user_belongs_role
(
    id         bigint unsigned auto_increment
        primary key,
    user_id    bigint   not null comment '用户id',
    role_id    bigint   not null comment '角色id',
    created_at datetime null,
    updated_at datetime null
);

create table user_login_log
(
    id         bigint unsigned auto_increment comment '主键'
        primary key,
    username   varchar(20)        not null comment '用户名',
    ip         varchar(45)        null comment '登录IP地址',
    os         varchar(255)       null comment '操作系统',
    browser    varchar(255)       null comment '浏览器',
    status     smallint default 1 not null comment '登录状态 (1成功 2失败)',
    message    varchar(50)        null comment '提示消息',
    login_time datetime           not null comment '登录时间',
    remark     varchar(255)       null comment '备注'
)
    comment '登录日志表';

create index user_login_log_username_index
    on user_login_log (username);

create table user_operation_log
(
    id           bigint unsigned auto_increment
        primary key,
    username     varchar(20)  not null comment '用户名',
    method       varchar(20)  not null comment '请求方式',
    router       varchar(500) not null comment '请求路由',
    service_name varchar(30)  not null comment '业务名称',
    ip           varchar(45)  null comment '请求IP地址',
    created_at   timestamp    null comment '创建时间',
    updated_at   timestamp    null comment '更新时间',
    remark       varchar(255) null comment '备注'
)
    comment '操作日志表';

create index user_operation_log_username_index
    on user_operation_log (username);


