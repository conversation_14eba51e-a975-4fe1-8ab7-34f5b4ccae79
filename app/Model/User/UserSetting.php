<?php

namespace App\Model\User;

use Hyperf\DbConnection\Model\Model as MineModel;


/**
* Class user_settings
* @property integer $id  
* @property integer $user_id  用户ID
* @property integer $merge_switch  合并开关 0:关闭 1:开启
* @property string $created_at  
* @property string $updated_at  
*/
class UserSetting extends MineModel
{
    protected ?string $table = 'user_settings';

    protected array $fillable = ['id','user_id','merge_switch','created_at','updated_at',];

    protected array $casts = ['id' => 'integer','user_id' => 'integer','merge_switch' => 'integer','created_at' => 'string','updated_at' => 'string',];
}