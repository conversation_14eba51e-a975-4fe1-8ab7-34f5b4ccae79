<?php

namespace App\Service\User;

use App\Service\IService;
use App\Repository\User\UserSettingRepository as Repository;



class UserSettingService extends IService
{
    public function __construct(
        protected readonly Repository $repository
    ) {}

    public function getRepository(): Repository
    {
        return $this->repository;
    }

    public function updateByUserId(int $userId, array $data)
    {
        return (bool) $this->repository->getQuery()->where('user_id',$userId)->first()?->update($data);
    }
}
