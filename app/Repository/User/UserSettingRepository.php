<?php

namespace App\Repository\User;

use App\Repository\IRepository;
use App\Model\User\UserSetting as Model;
use Hyperf\Database\Model\Builder;


/**
* UserSettingRepository
* @extends IRepository<Model>
*/
class UserSettingRepository extends IRepository
{
    public function __construct(
        protected readonly Model $model
    ) {}

    public function handleSearch(Builder $query, array $params): Builder
    {
                                                                                                            
        return $query;
    }
}
