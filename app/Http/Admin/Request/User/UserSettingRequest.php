<?php

namespace App\Http\Admin\Request\User;

use Hyperf\Swagger\Annotation\Property;
use Hyperf\Swagger\Annotation\Schema;
use Hyperf\Validation\Request\FormRequest;


#[Schema(
    title: '用户设置',
    description: '用户设置',
    properties: [
        new Property(
            property: 'merge_switch',
            description: '合并开关 0:关闭 1:开启',
            type: 'integer',
        ),
    ],
)]
class UserSettingRequest extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'merge_switch' => 'integer|in:0,1',
        ];
    }

    public function attributes(): array
    {
        return ['id' => '','user_id' => '用户ID','merge_switch' => '合并开关 0:关闭 1:开启','created_at' => '','updated_at' => '',];
    }

}