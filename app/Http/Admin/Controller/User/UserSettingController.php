<?php

namespace App\Http\Admin\Controller\User;

use App\Http\Admin\Request\IdRequest;
use App\Service\User\UserSettingService as Service;
use App\Http\Admin\Request\User\UserSettingRequest as Request;
use App\Http\Admin\Controller\AbstractController;
use App\Http\Common\Middleware\AccessTokenMiddleware;
use App\Http\Common\Result;
use App\Http\CurrentUser;
use App\Http\Admin\Middleware\PermissionMiddleware;
use Mine\Access\Attribute\Permission;
use Hyperf\HttpServer\Annotation\Middleware;

//use App\Annotation\ApiName;
//use App\Http\Common\Middleware\OperationHyperfRouterAnnotationMiddleware;
use App\Http\Common\Middleware\OperationMiddleware;
use Hyperf\Swagger\Annotation as OA;
use Mine\Swagger\Attributes\ResultResponse;
use Hyperf\Swagger\Annotation\Post;
use Hyperf\Swagger\Annotation\Put;
use Hyperf\Swagger\Annotation\Get;
use Hyperf\Swagger\Annotation\Delete;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\RequestBody;

//use Hyperf\HttpServer\Annotation\Controller;
//use Hyperf\HttpServer\Annotation\RequestMapping;

#[OA\Tag('{用户设置}')]
#[OA\HyperfServer('http')]
//#[Controller(prefix: '/admin')]
#[Middleware(middleware: AccessTokenMiddleware::class, priority: 100)]
#[Middleware(middleware: PermissionMiddleware::class, priority: 99)]
#[Middleware(middleware: OperationMiddleware::class, priority: 98)]
//#[Middleware(middleware: OperationHyperfRouterAnnotationMiddleware::class, priority: 98)]
final class UserSettingController extends AbstractController
{
    public function __construct(
        private readonly Service     $service,
        private readonly CurrentUser $currentUser
    )
    {
    }

//    #[Get(
//        path: '/admin/user/user_setting/list',
//        operationId: 'user:user_setting:list',
//        summary: '用户设置列表',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['用户设置'],
//    )]
////    #[ApiName(name: '用户设置列表')]
////    #[RequestMapping(path: 'user/user_setting/list', methods:"get")]
//    #[Permission(code: 'user:user_setting:list')]
//    public function pageList(): Result
//    {
//        return $this->success(
//            $this->service->page(
//                $this->getRequestData(),
//                $this->getCurrentPage(),
//                $this->getPageSize()
//            )
//        );
//    }


//    #[Post(
//        path: '/admin/user/user_setting',
//        operationId: 'user:user_setting:create',
//        summary: '新增用户设置',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['用户设置'],
//    )]
//    #[ResultResponse(instance: new Result())]
////    #[ApiName(name: '新增用户设置')]
////    #[RequestMapping(path: 'user/user_setting', methods:"post")]
//    #[Permission(code: 'user:user_setting:create')]
//    public function create(Request $request): Result
//    {
//        $this->service->create(array_merge($request->validated(), [
//            'created_by' => $this->currentUser->id(),
//        ]));
//        return $this->success();
//    }

    #[Post(
        path: '/admin/user/user_setting',
        operationId: 'user:user_setting:update',
        summary: '保存用户设置',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['用户设置'],
    )]
    #[ResultResponse(instance: new Result())]
    #[RequestBody(content: new JsonContent(ref: Request::class, title: '拆单请求'))]
//    #[ApiName(name: '保存用户设置')]
//    #[RequestMapping(path: 'user/user_setting/{id}', methods:"put")]
    #[Permission(code: 'user:user_setting:update')]
    public function save(Request $request): Result
    {
        $id = $this->currentUser->id();
        $this->service->updateByUserId($id, array_merge($request->validated(), [
            'updated_by' => $this->currentUser->id(),
        ]));
        return $this->success();
    }

    #[Get(
        path: '/admin/user/user_setting/current',
        operationId: 'user:user_setting:current',
        summary: '获取当前用户设置',
        security: [['Bearer' => [], 'ApiKey' => []]],
        tags: ['用户设置'],
    )]
    #[ResultResponse(instance: new Result())]
    #[Permission(code: 'user:user_setting:current')]
    public function getCurrentSetting(): Result
    {
        $userId = $this->currentUser->id();
        $setting = $this->service->getRepository()->getQuery()
            ->where('user_id', $userId)
            ->first();
        
//        if (!$setting) {
//            // 如果没有设置，创建一个默认设置
//            $setting = $this->service->create([
//                'user_id' => $userId,
//                'merge_switch' => 0,
//                'created_by' => $userId,
//            ]);
//        }
        
        return $this->success($setting);
    }

//    #[Delete(
//        path: '/admin/user/user_setting',
//        operationId: 'user:user_setting:delete',
//        summary: '删除用户设置',
//        security: [['Bearer' => [], 'ApiKey' => []]],
//        tags: ['用户设置'],
//    )]
//    #[ResultResponse(instance: new Result())]
//    #[RequestBody(content: new JsonContent(ref: IdRequest::class, title: '删除用户设置'))]
////    #[ApiName(name: '删除用户设置')]
////    #[RequestMapping(path: 'user/user_setting', methods:"delete")]
//    #[Permission(code: 'user:user_setting:delete')]
//    public function delete(IdRequest $request): Result
//    {
//        $newRequest = $request->validatedAndAssignNew();
//        $this->service->deleteByIdAndUser($this->currentUser->user(), $newRequest->id);
//        return $this->success();
//    }

}
